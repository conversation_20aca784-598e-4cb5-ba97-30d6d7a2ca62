package com.cjy.pyp.modules.activity.controller;

import java.util.Arrays;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.cjy.pyp.modules.activity.entity.AdTypeConfigEntity;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;

/**
 * 广告类型配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("activity/adtypeconfig")
public class AdTypeConfigController {
    @Autowired
    private AdTypeConfigService adTypeConfigService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("activity:adtypeconfig:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = adTypeConfigService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("activity:adtypeconfig:info")
    public R info(@PathVariable("id") Long id){
		AdTypeConfigEntity adTypeConfig = adTypeConfigService.getById(id);

        return R.ok().put("adTypeConfig", adTypeConfig);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("activity:adtypeconfig:save")
    public R save(@RequestBody AdTypeConfigEntity adTypeConfig){
		adTypeConfigService.save(adTypeConfig);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("activity:adtypeconfig:update")
    public R update(@RequestBody AdTypeConfigEntity adTypeConfig){
		adTypeConfigService.updateById(adTypeConfig);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("activity:adtypeconfig:delete")
    public R delete(@RequestBody Long[] ids){
		adTypeConfigService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    
    /**
     * 获取所有启用的广告类型配置（用于下拉选择）
     */
    @RequestMapping("/enabled")
    public R getEnabledConfigs(){
        return R.ok().put("list", adTypeConfigService.getEnabledConfigs());
    }

}
