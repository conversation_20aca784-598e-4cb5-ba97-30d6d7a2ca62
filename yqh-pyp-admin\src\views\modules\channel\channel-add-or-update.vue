<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="渠道名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="渠道名称"></el-input>
      </el-form-item>
      <el-form-item label="渠道编号" prop="code">
        <el-input v-model="dataForm.code" placeholder="渠道编号"></el-input>
      </el-form-item>
      <el-form-item label="上级渠道" prop="parentId">
        <el-select v-model="dataForm.parentId" placeholder="请选择上级渠道" clearable>
          <el-option
            v-for="channel in parentChannelList"
            :key="channel.id"
            :label="channel.name"
            :value="channel.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input v-model="dataForm.contactName" placeholder="联系人姓名"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="contactMobile">
        <el-input v-model="dataForm.contactMobile" placeholder="联系电话"></el-input>
      </el-form-item>
      <el-form-item label="联系邮箱" prop="contactEmail">
        <el-input v-model="dataForm.contactEmail" placeholder="联系邮箱"></el-input>
      </el-form-item>
      <el-form-item label="渠道地址" prop="address">
        <el-input v-model="dataForm.address" placeholder="渠道地址"></el-input>
      </el-form-item>
      <el-form-item label="佣金比例" prop="commissionRate">
        <el-input-number v-model="dataForm.commissionRate" :precision="4" :step="0.0001" :max="1" :min="0" placeholder="佣金比例"></el-input-number>
        <span style="margin-left: 10px; color: #999;">范围：0-1，如0.05表示5%</span>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">启用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="渠道描述" prop="description">
        <el-input v-model="dataForm.description" type="textarea" placeholder="渠道描述"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" type="textarea" placeholder="备注信息"></el-input>
      </el-form-item>

      <!-- 管理员信息提示 -->
      <el-divider>管理员账号信息</el-divider>
      <el-alert v-if="!dataForm.id"
        title="系统将自动创建渠道管理员账号"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;">
        <template slot>
          <p><strong>用户名：</strong>channel_{{ dataForm.code || '[渠道编号]' }}</p>
          <p><strong>默认密码：</strong>123456</p>
          <p><strong>说明：</strong>渠道创建成功后，管理员可使用此账号登录系统管理渠道业务员</p>
        </template>
      </el-alert>

      <el-alert v-if="dataForm.id"
        title="管理员账号管理"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px;">
        <template slot>
          <p><strong>当前用户名：</strong>channel_{{ originalCode || dataForm.code }}</p>
          <p v-if="dataForm.code !== originalCode"><strong>更新后用户名：</strong>channel_{{ dataForm.code }}</p>
          <p><strong>说明：</strong>如果修改了渠道编号，管理员用户名也会相应更新</p>
          <p><strong>管理：</strong>可在"渠道管理员"页面进行密码重置等操作</p>
        </template>
      </el-alert>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        code: '',
        parentId: '',
        contactName: '',
        contactMobile: '',
        contactEmail: '',
        address: '',
        commissionRate: 0,
        status: 1,
        description: '',
        remarks: ''
      },
      parentChannelList: [],
      originalCode: '', // 保存原始渠道编号
      dataRule: {
        name: [
          { required: true, message: '渠道名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '渠道编号不能为空', trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        contactMobile: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        contactEmail: [
          { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.getParentChannelList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/channel/channel/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.channel.name
              this.dataForm.code = data.channel.code
              this.dataForm.parentId = data.channel.parentId
              this.dataForm.contactName = data.channel.contactName
              this.dataForm.contactMobile = data.channel.contactMobile
              this.dataForm.contactEmail = data.channel.contactEmail
              this.dataForm.address = data.channel.address
              this.dataForm.commissionRate = data.channel.commissionRate
              this.dataForm.status = data.channel.status
              this.dataForm.description = data.channel.description
              this.dataForm.remarks = data.channel.remarks

              // 保存原始渠道编号
              this.originalCode = data.channel.code
            }
          })
        }
      })
    },
    // 获取上级渠道列表
    getParentChannelList() {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.parentChannelList = data.channelList || []
          // 过滤掉当前渠道，避免选择自己作为上级
          if (this.dataForm.id) {
            this.parentChannelList = this.parentChannelList.filter(item => item.id !== this.dataForm.id)
          }
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/channel/channel/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'code': this.dataForm.code,
              'parentId': this.dataForm.parentId || null,
              'contactName': this.dataForm.contactName,
              'contactMobile': this.dataForm.contactMobile,
              'contactEmail': this.dataForm.contactEmail,
              'address': this.dataForm.address,
              'commissionRate': this.dataForm.commissionRate,
              'status': this.dataForm.status,
              'description': this.dataForm.description,
              'remarks': this.dataForm.remarks
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
