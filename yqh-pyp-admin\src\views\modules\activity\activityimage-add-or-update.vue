<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增活动图片' : '修改活动图片'" :close-on-click-modal="false" :visible.sync="visible"
      width="800px">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
        label-width="120px">

        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片名称" prop="name">
              <el-input v-model="dataForm.name" placeholder="请输入图片名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="paixu">
              <el-input-number v-model="dataForm.paixu" :min="0" :max="9999" placeholder="排序值" style="width: 100%">
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 图片上传 -->
        <el-form-item label="图片文件" prop="mediaUrl">
          <div class="image-upload-section">
            <el-button type="primary" @click="openImageModal" :disabled="uploading">
              <i class="el-icon-picture"></i> {{ selectedImages.length > 0 ? '重新选择图片' : '选择图片' }}
            </el-button>

            <div v-if="selectedImages.length > 0" class="selected-image-preview">
              <div class="image-item">
                <img :src="selectedImages[0].url" :alt="selectedImages[0].url" />
                <div class="image-info">
                  <p class="image-name">{{ dataForm.name || '未命名图片' }}</p>
                  <p class="image-url">{{ selectedImages[0].url }}</p>
                  <p class="image-size" v-if="dataForm.fileSize">文件大小: {{ formatFileSize(dataForm.fileSize) }}</p>
                </div>
                <div class="image-actions">
                  <el-button size="mini" @click="previewImage(selectedImages[0].url)">
                    <i class="el-icon-zoom-in"></i> 预览
                  </el-button>
                  <el-button size="mini" type="danger" @click="removeImage">
                    <i class="el-icon-delete"></i> 删除
                  </el-button>
                </div>
              </div>
            </div>

            <div v-if="!selectedImages.length && !dataForm.mediaUrl" class="upload-tip">
              <i class="el-icon-picture-outline"></i>
              <p>请选择图片文件</p>
            </div>
          </div>
        </el-form-item>

        <!-- 类型选择 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片类型" prop="type">
              <el-radio-group v-model="dataForm.type">
                <el-radio :label="0">
                  <span class="radio-label">
                    <i class="el-icon-files"></i> 素材
                  </span>
                </el-radio>
                <el-radio :label="1">
                  <span class="radio-label">
                    <i class="el-icon-picture"></i> 成品
                  </span>
                </el-radio>
              </el-radio-group>
              <div class="type-description">
                <p v-if="dataForm.type === 0" class="type-desc material">素材：原始图片文件，可用于后期编辑制作</p>
                <p v-if="dataForm.type === 1" class="type-desc product">成品：最终制作完成的图片，可直接使用</p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用次数">
              <el-input v-model="dataForm.useCount" readonly>
                <template slot="append">次</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.id">
            <el-form-item label="关联文案" prop="activityTextId">
              <el-select v-model="dataForm.activityTextId" placeholder="选择关联文案" clearable filterable
                style="width: 100%">
                <el-option v-for="text in activityTexts" :key="text.id" :label="text.title || text.content"
                  :value="text.id">
                  <span style="float: left">{{ text.title || text.content }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ text.id }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 统计信息 -->
        <el-row :gutter="20" v-if="dataForm.id">
          <!-- <el-col :span="12">
          <el-form-item label="会议ID">
            <el-input v-model="dataForm.activityId" readonly></el-input>
          </el-form-item>
        </el-col> -->
        </el-row>

      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()" :loading="submitting">
          {{ submitting ? '保存中...' : '确定' }}
        </el-button>
      </span>

      <!-- 图片上传弹窗 -->
      <ImageUploadModal :visible.sync="imageModalVisible" :multiple="false" :max-count="1"
        :default-images="selectedImages" @confirm="handleImageConfirm" />

      <!-- 图片预览弹窗 -->
      <el-dialog :visible.sync="previewVisible" width="60%" append-to-body :z-index="3100">
        <img width="100%" :src="previewImageUrl" alt="图片预览">
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
export default {
  components: {
    ImageUploadModal: () => import("@/components/image-upload-modal")
  },
  data() {
    return {
      visible: false,
      submitting: false,
      uploading: false,
      imageModalVisible: false,
      previewVisible: false,
      previewImageUrl: '',
      selectedImages: [],
      activityTexts: [],
      dataForm: {
        repeatToken: '',
        id: 0,
        name: '',
        fileSize: 0,
        mediaUrl: '',
        activityId: '',
        paixu: 0,
        type: 0,
        useCount: 0,
        activityTextId: ''
      },
      dataRule: {
        name: [
          { required: true, message: '图片名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '图片名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        mediaUrl: [
          { required: true, message: '请选择图片文件', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择图片类型', trigger: 'change' }
        ],
        paixu: [
          { type: 'number', message: '排序必须为数字值', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id, activityId) {
      this.getToken()
      this.loadActivityTexts(activityId)
      this.dataForm.id = id || 0
      this.dataForm.activityId = activityId
      this.visible = true
      this.selectedImages = []

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.loadActivityImageInfo()
        } else {
          // 新增时的默认值
          this.dataForm.type = 0
          this.dataForm.paixu = 0
          this.dataForm.useCount = 0
        }
      })
    },

    loadActivityImageInfo() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activityimage/info/${this.dataForm.id}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          const info = data.activityImage
          this.dataForm.name = info.name
          this.dataForm.fileSize = info.fileSize || 0
          this.dataForm.mediaUrl = info.mediaUrl
          this.dataForm.paixu = info.paixu || 0
          this.dataForm.type = info.type || 0
          this.dataForm.useCount = info.useCount || 0
          this.dataForm.activityTextId = info.activityTextId || ''

          // 初始化已选择的图片
          if (info.mediaUrl) {
            this.selectedImages = [{
              id: `existing_${this.dataForm.id}`,
              url: info.mediaUrl,
              createDate: new Date().toISOString()
            }]
          }
        }
      }).catch(err => {
        this.$message.error('加载图片信息失败')
        console.error(err)
      })
    },

    loadActivityTexts(activityId) {
      // 加载活动相关的文案列表
      this.$http({
        url: this.$http.adornUrl('/activity/activitytext/list'),
        method: 'get',
        params: this.$http.adornParams({
          activityId: activityId,
          page: 1,
          limit: 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityTexts = data.page?.list || []
        }
      }).catch(err => {
        console.error('加载文案列表失败:', err)
        this.activityTexts = []
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm.repeatToken = data.result
        }
      }).catch(err => {
        console.error('获取token失败:', err)
      })
    },

    // 图片上传相关方法
    openImageModal() {
      this.imageModalVisible = true
    },

    handleImageConfirm(images) {
      if (images && images.length > 0) {
        this.selectedImages = images
        this.dataForm.mediaUrl = images[0].url

        // 如果图片名称为空，使用文件名
        if (!this.dataForm.name && images[0].url) {
          const fileName = images[0].url.split('/').pop().split('.')[0]
          this.dataForm.name = fileName
        }

        // 设置文件大小（如果有的话）
        if (images[0].fileSize) {
          this.dataForm.fileSize = images[0].fileSize
        } else {
          // 如果没有文件大小信息，尝试获取
          this.dataForm.fileSize = 0
        }
      }
    },

    removeImage() {
      this.selectedImages = []
      this.dataForm.mediaUrl = ''
      this.dataForm.fileSize = 0
    },

    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
    },

    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0.00 MB'
      const mb = bytes / (1024 * 1024)
      return mb.toFixed(2) + ' MB'
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.dataForm.mediaUrl) {
            this.$message.error('请选择图片文件')
            return
          }

          this.submitting = true

          const submitData = {
            'repeatToken': this.dataForm.repeatToken,
            'id': this.dataForm.id || undefined,
            'name': this.dataForm.name,
            'fileSize': this.dataForm.fileSize || 0,
            'mediaUrl': this.dataForm.mediaUrl,
            'activityId': this.dataForm.activityId,
            'paixu': this.dataForm.paixu || 0,
            'type': this.dataForm.type,
            'useCount': this.dataForm.useCount || 0,
            'activityTextId': this.dataForm.activityTextId || ''
          }

          this.$http({
            url: this.$http.adornUrl(`/activity/activityimage/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(submitData)
          }).then(({ data }) => {
            this.submitting = false
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg || '操作失败')
              if (data.msg !== '不能重复提交') {
                this.getToken()
              }
            }
          }).catch(err => {
            this.submitting = false
            this.$message.error('提交失败，请重试')
            console.error('提交失败:', err)
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.image-upload-section {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.image-upload-section:hover {
  border-color: #409EFF;
}

.selected-image-preview {
  margin-top: 20px;
}

.image-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: white;
  text-align: left;
}

.image-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #eee;
}

.image-info {
  flex: 1;
}

.image-name {
  font-weight: 500;
  color: #303133;
  margin: 0 0 5px 0;
}

.image-url {
  color: #909399;
  font-size: 12px;
  margin: 0 0 5px 0;
  word-break: break-all;
}

.image-size {
  color: #67C23A;
  font-size: 12px;
  margin: 0;
}

.image-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-tip {
  color: #909399;
  padding: 40px 0;
}

.upload-tip i {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
}

.upload-tip p {
  margin: 0;
  font-size: 14px;
}

.radio-label {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.radio-label i {
  font-size: 16px;
}

.type-description {
  margin-top: 8px;
}

.type-desc {
  margin: 0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.type-desc.material {
  background-color: #f0f9ff;
  color: #1890ff;
  border: 1px solid #d6f7ff;
}

.type-desc.product {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #d9f7be;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-item {
    flex-direction: column;
    text-align: center;
  }

  .image-actions {
    flex-direction: row;
    justify-content: center;
  }
}
</style>
