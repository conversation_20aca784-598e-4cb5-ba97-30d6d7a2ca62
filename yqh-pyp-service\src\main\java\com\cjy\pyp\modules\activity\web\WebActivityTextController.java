package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

/**
 * 活动文案素材Web接口
 */
@RestController
@RequestMapping("web/activity/activitytext")
public class WebActivityTextController extends AbstractController {
    
    @Autowired
    private ActivityTextService activityTextService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        try {
            PageUtils page = activityTextService.queryPage(params);
            return R.ok().put("page", page);
        } catch (Exception e) {
            return R.error("获取文案列表失败: " + e.getMessage());
        }
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        try {
            ActivityTextEntity activityText = activityTextService.getById(id);
            return R.ok().put("activityText", activityText);
        } catch (Exception e) {
            return R.error("获取文案信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody ActivityTextEntity activityText) {
        try {
            // 设置创建用户
            activityText.setCreateBy(getUserId());
            activityTextService.save(activityText);
            return R.ok();
        } catch (Exception e) {
            return R.error("保存文案失败: " + e.getMessage());
        }
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody ActivityTextEntity activityText) {
        try {
            // 设置更新用户
            activityText.setUpdateBy(getUserId());
            activityTextService.updateById(activityText);
            return R.ok();
        } catch (Exception e) {
            return R.error("更新文案失败: " + e.getMessage());
        }
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        try {
            activityTextService.removeByIds(Arrays.asList(ids));
            return R.ok();
        } catch (Exception e) {
            return R.error("删除文案失败: " + e.getMessage());
        }
    }

    /**
     * 生成AI文案
     */
    @RequestMapping("/generate")
    public R generateText(@RequestBody ActivityTextEntity activityText) {
        try {
            return activityTextService.generateText(activityText, getUserId());
        } catch (Exception e) {
            return R.error("生成文案失败: " + e.getMessage());
        }
    }

}
