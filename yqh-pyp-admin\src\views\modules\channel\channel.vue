<template>
  <div class="mod-channel">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="渠道名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.code" placeholder="渠道编号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="状态" clearable>
          <el-option label="启用" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('channel:channel:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('channel:channel:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overallStats.channelCount || 0 }}</div>
            <div class="stats-label">总渠道数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overallStats.totalSalesmanCount || 0 }}</div>
            <div class="stats-label">总业务员数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overallStats.totalOrders || 0 }}</div>
            <div class="stats-label">总订单数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (overallStats.totalAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">总销售额</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="渠道名称">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" label="渠道编号">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" label="后台账号">
        <template slot-scope="scope">
          <span>channel_{{ scope.row.code }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="contactName" header-align="center" align="center" label="联系人">
      </el-table-column>
      <el-table-column prop="contactMobile" header-align="center" align="center" label="联系电话">
      </el-table-column>
      <el-table-column prop="parentName" header-align="center" align="center" label="上级渠道">
        <template slot-scope="scope">
          <span>{{ scope.row.parentName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" header-align="center" align="center" label="层级">
      </el-table-column>
      <el-table-column prop="salesmanCount" header-align="center" align="center" label="业务员数">
      </el-table-column>
      <el-table-column prop="totalOrders" header-align="center" align="center" label="订单数">
      </el-table-column>
      <el-table-column prop="totalAmount" header-align="center" align="center" label="销售额">
        <template slot-scope="scope">
          <span>¥{{ (scope.row.totalAmount || 0).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small" type="success">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" width="180" label="创建时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('channel:channel:list')" type="text" size="small"
            @click="salesmanHandle(scope.row.id)">业务员</el-button>
          <el-button v-if="isAuth('channel:channel:list')" type="text" size="small"
            @click="customerHandle(scope.row.id)">客户</el-button>
            <!-- <el-button v-if="isAuth('channel:channel:list')" type="text" size="small"
            @click="activityHandle(scope.row.id)">活动</el-button> -->
          <el-button v-if="isAuth('channel:channel:update')" type="text" size="small"
            @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button v-if="isAuth('channel:channel:delete')" type="text" size="small"
            @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './channel-add-or-update'

export default {
  components: {
    AddOrUpdate
  },
  data() {
    return {
      dataForm: {
        name: '',
        code: '',
        status: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      overallStats: {},
      addOrUpdateVisible: false
    }
  },
  activated() {
    this.getDataList()
    this.getOverallStats()
  },
  methods: {
    activityHandle(channelId) {
      this.$router.push({
        name: 'channel-activity',
        query: {
          channelId: channelId
        }
      })
    },
    salesmanHandle(channelId) {
      this.$router.push({
        name: 'salesman-salesman',
        query: {
          channelId: channelId
        }
      })
    },
    customerHandle(channelId) {
      this.$router.push({
        name: 'channel-customer',
        query: {
          channelId: channelId
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/channel/channel/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'code': this.dataForm.code,
          'status': this.dataForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取总体统计
    getOverallStats() {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/stats'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.overallStats = data.stats || {}
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/channel/channel/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.stats-card {
  text-align: center;
}

.stats-item {
  padding: 10px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}
</style>
