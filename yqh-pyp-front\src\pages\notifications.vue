<template>
  <div class="notifications-page">
    <!-- 导航栏 -->
    <van-nav-bar 
      title="消息通知" 
      left-arrow 
      @click-left="$router.go(-1)"
    >
      <template #right>
        <van-button 
          type="primary" 
          size="mini" 
          @click="markAllAsRead"
          :disabled="unreadCount === 0"
        >
          全部已读
        </van-button>
      </template>
    </van-nav-bar>

    <!-- 筛选标签 -->
    <div class="filter-tabs">
      <van-tabs v-model="activeTab" @change="onTabChange">
        <van-tab title="全部" name="all"></van-tab>
        <van-tab title="过期提醒" name="expiration"></van-tab>
        <van-tab title="系统消息" name="system"></van-tab>
      </van-tabs>
    </div>

    <!-- 未读数量提示 -->
    <div v-if="unreadCount > 0" class="unread-banner">
      <van-icon name="info-o" />
      <span>您有 {{ unreadCount }} 条未读消息</span>
    </div>

    <!-- 通知列表 -->
    <div class="notification-content">
      <ActivityNotificationList
        ref="notificationList"
        :activity-id="currentActivityId"
        :expiration-only="activeTab === 'expiration'"
        @notification-click="handleNotificationClick"
        @show-renewal="handleShowRenewal"
      />
    </div>

    <!-- 续费弹窗 -->
    <ActivityRenewalDialog
      v-model="renewalDialogVisible"
      :activity-id="renewalActivityId"
      :activity-name="renewalActivityName"
      :expiration-status="renewalExpirationStatus"
      :renewal-packages="renewalPackages"
      @renewal="handleRenewal"
    />
  </div>
</template>

<script>
import ActivityNotificationList from '@/components/ActivityNotificationList.vue';
import ActivityRenewalDialog from '@/components/ActivityRenewalDialog.vue';

export default {
  name: 'NotificationsPage',
  components: {
    ActivityNotificationList,
    ActivityRenewalDialog
  },
  data() {
    return {
      activeTab: 'all',
      unreadCount: 0,
      renewalDialogVisible: false,
      renewalActivityId: null,
      renewalActivityName: '',
      renewalExpirationStatus: null,
      renewalPackages: []
    };
  },
  computed: {
    currentActivityId() {
      return this.$store.state.activity.selectedActivityId;
    }
  },
  mounted() {
    this.loadUnreadCount();
  },
  methods: {
    async loadUnreadCount() {
      if (!this.currentActivityId) return;
      
      try {
        const response = await this.$fly.get('/activity/activitynotify/noReadCount', {
          params: { activityId: this.currentActivityId }
        });
        
        if (response.data.code === 200) {
          this.unreadCount = response.data.result || 0;
        }
      } catch (error) {
        console.error('获取未读数量失败:', error);
      }
    },

    onTabChange(name) {
      this.activeTab = name;
      // 刷新通知列表
      this.$nextTick(() => {
        if (this.$refs.notificationList) {
          this.$refs.notificationList.refresh();
        }
      });
    },

    async markAllAsRead() {
      if (!this.currentActivityId || this.unreadCount === 0) return;
      
      try {
        this.$toast.loading({
          message: '标记中...',
          forbidClick: true
        });

        // 获取所有未读通知的ID
        const response = await this.$fly.get('/activity/activitynotify/list', {
          params: {
            activityId: this.currentActivityId,
            read: 0, // 只获取未读的
            page: 1,
            limit: 1000 // 获取所有未读
          }
        });

        if (response.data.code === 200) {
          const unreadNotifications = response.data.data || [];
          if (unreadNotifications.length > 0) {
            const ids = unreadNotifications.map(n => n.id).join(',');
            
            await this.$fly.post('/activity/activitynotify/read', { id: ids });
            
            this.$toast.success('已全部标记为已读');
            this.unreadCount = 0;
            
            // 刷新通知列表
            if (this.$refs.notificationList) {
              this.$refs.notificationList.refresh();
            }
          }
        }
      } catch (error) {
        console.error('标记已读失败:', error);
        this.$toast.fail('操作失败');
      } finally {
        this.$toast.clear();
      }
    },

    handleNotificationClick(notification) {
      // 更新未读数量
      if (notification.read === 0) {
        this.unreadCount = Math.max(0, this.unreadCount - 1);
      }
    },

    async handleShowRenewal(activityId) {
      try {
        // 获取活动信息
        const activity = this.$store.state.activity.userActivities.find(a => a.id === activityId);
        if (!activity) {
          this.$toast.fail('活动不存在');
          return;
        }

        this.renewalActivityId = activityId;
        this.renewalActivityName = activity.name;

        // 获取过期状态
        const statusResponse = await this.$fly.get(`/web/activity/expiration/status/${activityId}`);
        if (statusResponse.data.code === 200) {
          this.renewalExpirationStatus = statusResponse.data.status;
        }

        // 获取续费套餐
        const packagesResponse = await this.$fly.get('/web/activity/expiration/renewalPackages');
        if (packagesResponse.data.code === 200) {
          this.renewalPackages = packagesResponse.data.packages || [];
        }

        this.renewalDialogVisible = true;
      } catch (error) {
        console.error('获取续费信息失败:', error);
        this.$toast.fail('获取续费信息失败');
      }
    },

    async handleRenewal(orderData) {
      try {
        this.$toast.loading({
          message: '创建订单中...',
          forbidClick: true,
          duration: 0
        });

        const response = await this.$store.dispatch('activity/createRenewalOrder', orderData);
        
        this.$toast.clear();
        
        if (response.code === 200) {
          this.renewalDialogVisible = false;
          this.$toast.success('订单创建成功');
          
          // 跳转到支付页面
          this.$router.push({
            path: '/payment',
            query: {
              orderSn: response.orderSn,
              amount: response.amount,
              type: 'renewal'
            }
          });
        } else {
          this.$toast.fail(response.msg || '创建订单失败');
        }
      } catch (error) {
        this.$toast.clear();
        console.error('续费失败:', error);
        this.$toast.fail(error.message || '续费失败');
      }
    }
  }
};
</script>

<style scoped>
.notifications-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.filter-tabs {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.unread-banner {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 14px;
}

.unread-banner .van-icon {
  margin-right: 8px;
}

.notification-content {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .notification-content {
    padding: 12px;
  }
  
  .unread-banner {
    padding: 10px 12px;
    font-size: 13px;
  }
}
</style>
