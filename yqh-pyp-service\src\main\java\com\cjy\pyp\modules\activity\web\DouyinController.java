package com.cjy.pyp.modules.activity.web;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.utils.RedisUtils;
import com.cjy.pyp.common.utils.RestTemplateUtil;

import com.cjy.pyp.modules.activity.service.ActivityVideoService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.service.ActivityVideoPreGenerateService;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEntity;
import com.cjy.pyp.modules.activity.config.DouyinConfig;
import com.cjy.pyp.modules.sys.controller.AbstractController;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.List;
import java.security.MessageDigest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/web/douyin")
public class DouyinController extends AbstractController {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private RestTemplateUtil restTemplateUtil;

    @Autowired
    private ActivityVideoService activityVideoService;

    @Autowired
    private DouyinConfig douyinConfig;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;
    @Autowired
    private ActivityVideoPreGenerateService activityVideoPreGenerateService;
    @Autowired
    private ActivityService activityService;

    /**
     * 获取抖音分享Schema
     * 
     * @param activityId 活动ID
     * @param videoType  视频类型：0-素材，1-成品
     * @return 分享Schema
     * @throws Exception
     */
    @GetMapping("/getShareSchema")
    public R getShareSchema(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "videoType", required = false) Integer videoType,
            @RequestParam(value = "shareToPublish", required = false) Integer shareToPublish)
            throws Exception {

        if (activityId == null) {
            return R.error("请传入有效的活动ID");
        }

        // 检查抖音功能是否启用
        if (!douyinConfig.getEnabled()) {
            return R.error("抖音分享功能未启用");
        }

        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                "抖音转发"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }

        if (shareToPublish == null) {
            shareToPublish = douyinConfig.getShareToPublish();
        }

        try {
            // 1. 获取client_token
            String clientToken = getClientToken();
            if (StringUtils.isEmpty(clientToken)) {
                return R.error("获取client_token失败");
            }

            // 2. 获取ticket
            String ticket = getTicket(clientToken);
            if (StringUtils.isEmpty(ticket)) {
                return R.error("获取ticket失败");
            }

            // 3. 获取成品视频
            ActivityVideoEntity video = activityVideoService.findByActivityIdAndPlatformNoUse(activityId, "douyin");
            if (video == null) {
                // 生成视频
                activityVideoPreGenerateService.preGenerateVideoAsync(activityId,"douyin", null);
                return R.error("未找到可分享的视频，请稍后再试");
            }

            // 4. 生成分享Schema
            String schema = generateShareSchema(ticket, video, shareToPublish,activityId);

            Map<String, Object> result = new HashMap<>();
            result.put("schema", schema);
            result.put("videoInfo", video);
            activityVideoService.incrementUseCount(video.getId());
            return R.ok().put("result", result);

        } catch (Exception e) {
            e.printStackTrace();
            return R.error("生成分享Schema失败: " + e.getMessage());
        }
    }

    /**
     * 获取client_token
     */
    private String getClientToken() {
        try {
            // 检查配置是否有效
            if (!douyinConfig.isConfigValid()) {
                System.err.println("抖音配置无效，请检查appKey和appSecret");
                return null;
            }

            // 先从缓存中获取
            String cacheKey = douyinConfig.getClientTokenCacheKey();
            String cachedToken = redisUtils.get(cacheKey);
            if (StringUtils.isNotEmpty(cachedToken)) {
                return cachedToken;
            }

            // 调用抖音API获取client_token
            String url = "https://open.douyin.com/oauth/client_token/";
            Map<String, Object> params = new HashMap<>();
            params.put("client_key", douyinConfig.getAppKey());
            params.put("client_secret", douyinConfig.getAppSecret());
            params.put("grant_type", "client_credential");

            String response = restTemplateUtil.postForm(url, params);
            JSONObject jsonResponse = JSON.parseObject(response);
            String message = jsonResponse.getString("message");
            if (StringUtils.isNotEmpty(message) && message.equals("success")) {
                JSONObject data = jsonResponse.getJSONObject("data");

                if (data.containsKey("access_token")) {
                    String accessToken = data.getString("access_token");
                    Integer expiresIn = data.getInteger("expires_in");

                    // 缓存token，使用配置的缓存时间，但不超过实际过期时间
                    redisUtils.set(cacheKey, accessToken, expiresIn -1);
                    return accessToken;
                } else {
                    System.err.println("获取client_token失败: " + response);
                    return null;
                }
            } else {
                System.err.println("获取client_token失败: " + response);
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取ticket
     */
    private String getTicket(String clientToken) {
        try {
            // 先从缓存中获取
            String cacheKey = douyinConfig.getTicketCacheKey();
            String cachedTicket = redisUtils.get(cacheKey);
            if (StringUtils.isNotEmpty(cachedTicket)) {
                return cachedTicket;
            }

            // 调用抖音API获取ticket
            String url = "https://open.douyin.com/open/getticket/?access_token=" + clientToken;

            String response = restTemplateUtil.get(url);
            JSONObject jsonResponse = JSON.parseObject(response);

            if (jsonResponse.getJSONObject("data") != null) {
                String ticket = jsonResponse.getJSONObject("data").getString("ticket");
                Integer expiresIn = jsonResponse.getJSONObject("data").getInteger("expires_in");

                // 缓存ticket，使用配置的缓存时间，但不超过实际过期时间
                redisUtils.set(cacheKey, ticket, expiresIn -1);
                return ticket;
            } else {
                System.err.println("获取ticket失败: " + response);
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成分享Schema
     */
    private String generateShareSchema(String ticket, ActivityVideoEntity video, Integer shareToPublish ,Long activityId) {
        try {
            ActivityEntity activityEntitiy = activityService.getById(activityId);
            // 生成随机字符串和时间戳
            String nonceStr = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);

            // 生成签名
            String signature = generateSignature(nonceStr, ticket, timestamp);

            // 构建Schema参数
            StringBuilder schemaBuilder = new StringBuilder();
            schemaBuilder.append("snssdk1128://openplatform/share?");
            schemaBuilder.append("share_type=h5");
            schemaBuilder.append("&client_key=").append(URLEncoder.encode(douyinConfig.getAppKey(), "UTF-8"));
            schemaBuilder.append("&nonce_str=").append(URLEncoder.encode(nonceStr, "UTF-8"));
            schemaBuilder.append("&timestamp=").append(URLEncoder.encode(timestamp, "UTF-8"));
            schemaBuilder.append("&signature=").append(URLEncoder.encode(signature, "UTF-8"));

            // 添加视频路径
            if (StringUtils.isNotEmpty(video.getMediaUrl())) {
                schemaBuilder.append("&video_path=").append(URLEncoder.encode(video.getMediaUrl(), "UTF-8"));
            }

            // 添加标题
            if (StringUtils.isNotEmpty(video.getName())) {
                schemaBuilder.append("&title=").append(URLEncoder.encode(video.getName(), "UTF-8"));
            }

            // 是否直接分享到发布页
            if (shareToPublish != null && shareToPublish == 1) {
                schemaBuilder.append("&share_to_publish=1");
            }

            // 添加POI地理位置信息（如果有的话）
            if (StringUtils.isNotEmpty(activityEntitiy.getDouyinPoi())) {
                schemaBuilder.append("&poi_id=").append(URLEncoder.encode(activityEntitiy.getDouyinPoi(), "UTF-8"));
            }

            return schemaBuilder.toString();

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成MD5签名
     */
    private String generateSignature(String nonceStr, String ticket, String timestamp) {
        try {
            // 按字典序排序并拼接
            String string1 = "nonce_str=" + nonceStr + "&ticket=" + ticket + "&timestamp=" + timestamp;

            // MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(string1.getBytes(StandardCharsets.UTF_8));

            // 转换为16进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
