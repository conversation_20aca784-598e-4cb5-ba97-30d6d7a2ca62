package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.config.TaskExcutor;
import com.cjy.pyp.modules.activity.dto.VideoEditRequest;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEntity;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEntity;
import com.cjy.pyp.modules.activity.service.ActivityVideoPreGenerateService;
import com.cjy.pyp.modules.activity.service.ActivityVideoService;
import com.cjy.pyp.modules.activity.service.ActivityVideoPreGenerateService;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import com.cjy.pyp.modules.activity.service.DeepSeekApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 文案预生成服务实现
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class ActivityVideoPreGenerateServiceImpl implements ActivityVideoPreGenerateService {
    
    @Autowired
    private ActivityVideoService activityVideoService;
    
    @Override
    @Async
    public void preGenerateVideoAsync( Long activityId, String platform, Long userId) {
        
        // 使用线程池异步执行预生成任务
        TaskExcutor.submit(() -> {
            try {
                // 预生成2条文案
                for (int i = 1; i <= 2; i++) {
                    // TODO: 实现预生成视频的逻辑
                    VideoEditRequest request = new VideoEditRequest();
                    request.setActivityId(activityId);
                    request.setPlatform(platform);
                    activityVideoService.submitVideoEdit(request, userId);
                    
                    // 每条文案生成间隔1秒，避免API调用过于频繁
                    Thread.sleep(1000);
                }
                        
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
    
}
