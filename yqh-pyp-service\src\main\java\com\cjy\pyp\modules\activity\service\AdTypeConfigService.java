package com.cjy.pyp.modules.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.activity.entity.AdTypeConfigEntity;

import java.util.List;
import java.util.Map;

/**
 * 广告类型配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-30
 */
public interface AdTypeConfigService extends IService<AdTypeConfigEntity> {

    PageUtils queryPage(Map<String, Object> params);
    
    /**
     * 获取所有启用的广告类型配置
     * @return 广告类型配置列表
     */
    List<AdTypeConfigEntity> getEnabledConfigs();
    
    /**
     * 根据类型编码获取配置
     * @param typeCode 类型编码
     * @return 广告类型配置
     */
    AdTypeConfigEntity getByTypeCode(String typeCode);
    
    /**
     * 构建完整的Prompt
     * @param typeCode 类型编码
     * @param keyword 关键词
     * @param nameMode 标题生成模式（ai: AI生成, manual: 手动填写）
     * @param manualTitle 手动填写的标题（当nameMode为manual时使用）
     * @return 完整的Prompt
     */
    String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle);

    /**
     * 构建完整的Prompt（包含用户自定义输入）
     * @param typeCode 类型编码
     * @param keyword 关键词
     * @param nameMode 标题生成模式（ai: AI生成, manual: 手动填写）
     * @param manualTitle 手动填写的标题（当nameMode为manual时使用）
     * @param userCustomInput 用户自定义输入内容
     * @return 完整的Prompt
     */
    String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle, String userCustomInput);
}
