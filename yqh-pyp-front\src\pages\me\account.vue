<template>
  <div class="account-page">
    <!-- 活动切换器 -->
    <div class="activity-selector">
      <van-dropdown-menu>
        <van-dropdown-item v-model="selectedActivityId" :options="activityOptions" @change="onActivityChange">
          <template #title>
            <span class="activity-title">{{ currentActivityName || '选择活动' }}</span>
            <van-icon name="arrow-down" />
          </template>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>

    <!-- 账户余额卡片 -->
    <div class="balance-card">
      <div class="balance-header">
        <h2>我的账户</h2>
        <van-icon name="question-o" @click="showHelp" />
      </div>
      <div class="balance-content">
        <div class="balance-item">
          <div class="balance-value">{{ accountInfo.allCount || 0 }}</div>
          <div class="balance-label">总次数</div>
        </div>
        <div class="balance-item">
          <div class="balance-value">{{ accountInfo.useCount || 0 }}</div>
          <div class="balance-label">已使用</div>
        </div>
        <div class="balance-item">
          <div class="balance-value">{{ (accountInfo.allCount || 0) - (accountInfo.useCount || 0) }}</div>
          <div class="balance-label">剩余次数</div>
        </div>
      </div>
      <div class="balance-actions">
        <van-button type="primary" size="large" @click="togglePackages">
          <van-icon name="plus" />
          {{ showPackages ? '收起套餐' : '立即充值' }}
        </van-button>
      </div>
    </div>

    <!-- 充值套餐 -->
    <div class="packages-section" v-if="showPackages">
      <div class="section-header">
        <h3>充值套餐</h3>
        <van-button size="small" @click="showPackages = false">收起</van-button>
      </div>

      <!-- 推荐套餐 -->
      <!-- <div v-if="recommendedPackages.length > 0" class="package-group">
        <h4>推荐套餐</h4>
        <div class="package-list">
          <div
            v-for="pkg in recommendedPackages"
            :key="pkg.id"
            class="package-item"
            @click="selectPackage(pkg)">
            <div class="package-header">
              <div class="package-name">{{ pkg.name }}</div>
              <div class="package-price">¥{{ pkg.price }}</div>
            </div>
            <div class="package-content">
              <div class="package-count">{{ pkg.countValue }}次</div>
              <div class="package-desc">{{ pkg.description || '暂无描述' }}</div>
            </div>
            <van-tag v-if="pkg.isRecommended" type="danger" size="mini">推荐</van-tag>
          </div>
        </div>
      </div> -->

      <!-- 次数充值套餐 -->
      <div v-if="rechargeCountPackages.length > 0" class="package-group">
        <h4>次数充值</h4>
        <div class="package-list">
          <div
            v-for="pkg in rechargeCountPackages"
            :key="pkg.id"
            class="package-item"
            @click="selectPackage(pkg)">
            <div class="package-header">
              <div class="package-name">{{ pkg.name }}</div>
              <div class="package-price">¥{{ pkg.price }}</div>
            </div>
            <div class="package-content">
              <div class="package-count">{{ pkg.countValue }}次</div>
              <div class="package-desc">{{ pkg.description || '暂无描述' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 创建活动套餐 -->
      <div v-if="createActivityPackages.length > 0" class="package-group">
        <h4>创建活动套餐</h4>
        <div class="package-list">
          <div
            v-for="pkg in createActivityPackages"
            :key="pkg.id"
            class="package-item"
            @click="selectPackage(pkg)">
            <div class="package-header">
              <div class="package-name">{{ pkg.name }}</div>
              <div class="package-price">¥{{ pkg.price }}</div>
            </div>
            <div class="package-content">
              <div class="package-count">{{ pkg.countValue }}次</div>
              <div class="package-desc">{{ pkg.description || '暂无描述' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 记录类型切换 -->
    <div class="record-tabs">
      <van-tabs v-model="activeTab" @click="handleTabClick">
        <van-tab title="充值记录" name="recharge">
          <!-- 充值记录状态子tab -->
          <van-tabs v-model="rechargeStatusTab" @click="handleRechargeStatusTabClick" class="status-sub-tabs">
            <van-tab title="已支付" name="paid">
              <div class="record-list">
                <van-list
                  v-model="paidRechargeLoading"
                  :finished="paidRechargeFinished"
                  finished-text="没有更多了"
                  @load="loadPaidRechargeRecords">
                  <div
                    v-for="item in paidRechargeList"
                    :key="item.id"
                    class="record-item">
                    <div class="record-header">
                      <div class="record-title">{{ item.orderSn }}</div>
                      <div class="record-amount">+{{ item.countValue }}次</div>
                    </div>
                    <div class="record-content">
                      <div class="record-type">
                        <van-tag :type="getRechargeTypeTag(item.rechargeType).type">
                          {{ getRechargeTypeTag(item.rechargeType).text }}
                        </van-tag>
                      </div>
                      <div class="record-price">¥{{ item.payAmount || item.amount }}</div>
                    </div>
                    <!-- 使用情况 -->
                    <div class="record-usage">
                      <div class="usage-item">
                        <span class="usage-label">已使用：</span>
                        <span class="usage-value used">{{ item.usedCount || 0 }}次</span>
                      </div>
                      <div class="usage-item">
                        <span class="usage-label">剩余：</span>
                        <span class="usage-value remaining">{{ (item.countValue || 0) - (item.usedCount || 0) }}次</span>
                      </div>
                      <div class="usage-item">
                        <span class="usage-label">使用率：</span>
                        <span class="usage-value rate">{{ getUsagePercentage(item) }}%</span>
                      </div>
                    </div>
                    <div class="record-footer">
                      <div class="record-time">{{ formatDate(item.createOn) }}</div>
                      <div class="record-actions">
                        <van-tag class="status-tag" :type="getRechargeStatusTag(item.status).type">
                          {{ getRechargeStatusTag(item.status).text }}
                        </van-tag>
                        <!-- 退款按钮：只有用户自己充值的且已支付的记录才显示 -->
                        <div
                          v-if="canRefund(item)"
                          :class="['refund-btn', getRefundBtnClass(item)]"
                          @click="handleRefund(item)">
                          <van-icon name="refund-o" />
                          <span>退款</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </van-list>
              </div>
            </van-tab>

            <van-tab title="退款订单" name="refund">
              <div class="record-list">
                <van-list
                  v-model="refundRechargeLoading"
                  :finished="refundRechargeFinished"
                  finished-text="没有更多了"
                  @load="loadRefundRechargeRecords">
                  <div
                    v-for="item in refundRechargeList"
                    :key="item.id"
                    class="record-item">
                    <div class="record-header">
                      <div class="record-title">{{ item.orderSn }}</div>
                      <div class="record-amount">+{{ item.countValue }}次</div>
                    </div>
                    <div class="record-content">
                      <div class="record-type">
                        <van-tag :type="getRechargeTypeTag(item.rechargeType).type">
                          {{ getRechargeTypeTag(item.rechargeType).text }}
                        </van-tag>
                      </div>
                      <div class="record-price">¥{{ item.payAmount || item.amount }}</div>
                    </div>
                    <!-- 退款订单也显示使用情况 -->
                    <div v-if="item.status === 3" class="record-usage">
                      <div class="usage-item">
                        <span class="usage-label">已使用：</span>
                        <span class="usage-value used">{{ item.usedCount || 0 }}次</span>
                      </div>
                      <div class="usage-item">
                        <span class="usage-label">退款前剩余：</span>
                        <span class="usage-value remaining">{{ (item.countValue || 0) - (item.usedCount || 0) }}次</span>
                      </div>
                      <div class="usage-item">
                        <span class="usage-label">使用率：</span>
                        <span class="usage-value rate">{{ getUsagePercentage(item) }}%</span>
                      </div>
                    </div>
                    <div class="record-footer">
                      <div class="record-time">{{ formatDate(item.createOn) }}</div>
                      <div class="record-actions">
                        <van-tag class="status-tag" :type="getRechargeStatusTag(item.status).type">
                          {{ getRechargeStatusTag(item.status).text }}
                        </van-tag>
                      </div>
                    </div>
                  </div>
                </van-list>
              </div>
            </van-tab>

            <van-tab title="全部" name="all">
              <div class="record-list">
                <van-list
                  v-model="allRechargeLoading"
                  :finished="allRechargeFinished"
                  finished-text="没有更多了"
                  @load="loadAllRechargeRecords">
                  <div
                    v-for="item in allRechargeList"
                    :key="item.id"
                    class="record-item">
                    <div class="record-header">
                      <div class="record-title">{{ item.orderSn }}</div>
                      <div class="record-amount">+{{ item.countValue }}次</div>
                    </div>
                    <div class="record-content">
                      <div class="record-type">
                        <van-tag :type="getRechargeTypeTag(item.rechargeType).type">
                          {{ getRechargeTypeTag(item.rechargeType).text }}
                        </van-tag>
                      </div>
                      <div class="record-price">¥{{ item.payAmount || item.amount }}</div>
                    </div>
                    <!-- 使用情况 - 已支付和已退款的才显示 -->
                    <div v-if="item.status === 1 || item.status === 3" class="record-usage">
                      <div class="usage-item">
                        <span class="usage-label">已使用：</span>
                        <span class="usage-value used">{{ item.usedCount || 0 }}次</span>
                      </div>
                      <div class="usage-item">
                        <span class="usage-label">{{ item.status === 3 ? '退款前剩余：' : '剩余：' }}</span>
                        <span class="usage-value remaining">{{ (item.countValue || 0) - (item.usedCount || 0) }}次</span>
                      </div>
                      <div class="usage-item">
                        <span class="usage-label">使用率：</span>
                        <span class="usage-value rate">{{ getUsagePercentage(item) }}%</span>
                      </div>
                    </div>
                    <div class="record-footer">
                      <div class="record-time">{{ formatDate(item.createOn) }}</div>
                      <div class="record-actions">
                        <van-tag class="status-tag" :type="getRechargeStatusTag(item.status).type">
                          {{ getRechargeStatusTag(item.status).text }}
                        </van-tag>
                        <!-- 待支付状态显示去支付按钮 -->
                        <van-button
                          v-if="item.status === 0"
                          type="primary"
                          size="mini"
                          @click="goToPay(item)">
                          去支付
                        </van-button>
                        <!-- 已支付状态显示退款按钮 -->
                        <div
                          v-if="canRefund(item)"
                          :class="['refund-btn', getRefundBtnClass(item)]"
                          @click="handleRefund(item)">
                          <van-icon name="refund-o" />
                          <span>退款</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </van-list>
              </div>
            </van-tab>
          </van-tabs>
        </van-tab>

        <van-tab title="使用记录" name="usage">
          <div class="record-list">
            <van-list
              v-model="usageLoading"
              :finished="usageFinished"
              finished-text="没有更多了"
              @load="loadUsageRecords">
              <div
                v-for="item in usageList"
                :key="item.id"
                class="record-item">
                <div class="record-header">
                  <div class="record-title">
                    <van-tag :type="getUsageTypeTag(item.usageType).type">
                      {{ getUsageTypeTag(item.usageType).text }}
                    </van-tag>
                  </div>
                  <div class="record-amount">-{{ item.usageCount }}次</div>
                </div>
                <div class="record-content">
                  <div class="record-desc">{{ item.description || '暂无描述' }}</div>
                </div>
                <div class="record-footer">
                  <div class="record-time">{{ formatDate(item.createOn) }}</div>
                </div>
              </div>
            </van-list>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <!-- 活动名称输入对话框 -->
    <van-dialog
      v-model="showActivityNameDialogVisible"
      title="创建活动套餐"
      show-cancel-button
      confirm-button-text="确认购买"
      cancel-button-text="取消"
      @confirm="confirmCreateActivity"
      @cancel="cancelCreateActivity">
      <div class="activity-name-dialog">
        <div class="package-info" v-if="selectedPackageForActivity">
          <p><strong>套餐：</strong>{{ selectedPackageForActivity.name }}</p>
          <p><strong>价格：</strong>¥{{ selectedPackageForActivity.price }}</p>
          <p><strong>次数：</strong>{{ selectedPackageForActivity.countValue }}次</p>
        </div>
        <div class="input-section">
          <van-field
            v-model="activityNameInput"
            label="活动名称"
            placeholder="请输入活动名称"
            maxlength="50"
            show-word-limit
            :error-message="activityNameError"
            @input="validateActivityName"
          />
        </div>
      </div>
    </van-dialog>

  </div>
</template>

<script>
import dateUtil from '../../js/date'
import { checkAndRedirectToBindMobile, handleMobileError } from '@/js/mobileChecker.js'

export default {
  name: 'Account',
  data() {
    return {
      activityId: null,
      accountInfo: {
        allCount: 0,
        useCount: 0,
        activityName: '',
        userId: null
      },
      activeTab: 'recharge',
      showPackages: false,
      // 套餐数据
      recommendedPackages: [],
      rechargeCountPackages: [],
      createActivityPackages: [],
      // 充值记录状态tab
      rechargeStatusTab: 'paid',
      // 已支付充值记录
      paidRechargeList: [],
      paidRechargeLoading: false,
      paidRechargeFinished: false,
      paidRechargePage: 1,
      // 退款订单记录
      refundRechargeList: [],
      refundRechargeLoading: false,
      refundRechargeFinished: false,
      refundRechargePage: 1,
      // 全部充值记录
      allRechargeList: [],
      allRechargeLoading: false,
      allRechargeFinished: false,
      allRechargePage: 1,
      // 原有充值记录（保持兼容性）
      rechargeList: [],
      rechargeLoading: false,
      rechargeFinished: false,
      rechargePage: 1,
      // 使用记录
      usageList: [],
      usageLoading: false,
      usageFinished: false,
      usagePage: 1,
      // 防重复加载标志
      isLoadingRecords: false,
      // 充值类型映射
      rechargeTypeMap: {
        1: { text: '套餐充值', type: 'primary' },
        2: { text: '自定义充值', type: 'success' },
        3: { text: '系统赠送', type: 'warning' },
        4: { text: '创建活动套餐', type: 'danger' }
      },
      // 充值状态映射
      rechargeStatusMap: {
        0: { text: '待支付', type: 'warning' },
        1: { text: '已支付', type: 'success' },
        2: { text: '已取消', type: 'danger' },
        3: { text: '已退款', type: 'warning' }
      },
      // 使用类型映射
      usageTypeMap: {
        1: { text: '生成文案', type: 'primary' },
        2: { text: '生成视频', type: 'success' },
        4: { text: '生成图文成品', type: 'success' },
        3: { text: '转发', type: 'warning' }
      },
      // 活动名称输入对话框相关
      showActivityNameDialogVisible: false,
      selectedPackageForActivity: null,
      activityNameInput: '',
      activityNameError: ''
    }
  },
  computed: {
    // 从 store 中获取活动相关状态
    userActivities() {
      return this.$store.state.activity.userActivities;
    },
    selectedActivityId() {
      return this.$store.state.activity.selectedActivityId;
    },
    currentActivity() {
      return this.$store.state.activity.currentActivity;
    },
    activityOptions() {
      return this.$store.state.activity.activityOptions;
    },
    currentActivityName() {
      return this.$store.getters['activity/currentActivityName'];
    },
    hasActivities() {
      return this.$store.getters['activity/hasActivities'];
    }
  },
  watch: {
    // 监听当前活动变化
    currentActivity: {
      handler(newActivity, oldActivity) {
        if (newActivity && oldActivity && newActivity.id !== oldActivity.id) {
          console.log('Account页面检测到活动变化:', newActivity);
          // 更新本地的 activityId
          this.activityId = newActivity.id;
          // 重新加载页面数据
          this.initializeData();
        }
      },
      deep: true
    },
    // 监听选中的活动ID变化
    selectedActivityId(newId, oldId) {
      if (newId !== oldId && newId) {
        console.log('Account页面检测到选中活动ID变化:', newId);
        // 更新本地的 activityId
        this.activityId = newId;
      }
    }
  },
  mounted() {
    document.title = '我的账户'

    console.log('=== Account页面 mounted ===');
    console.log('URL中的activityId:', this.$route.query.activityId);
    console.log('Store中的selectedActivityId:', this.$store.state.activity.selectedActivityId);

    // 优先使用URL参数中的activityId，如果没有则使用缓存的活动ID
    const urlActivityId = this.$route.query.activityId
    if (urlActivityId) {
      this.activityId = urlActivityId
      console.log('使用URL中的activityId:', this.activityId);
    } else {
      // 如果URL中没有activityId，尝试从store中获取当前选中的活动ID
      const currentSelectedId = this.$store.state.activity.selectedActivityId
      if (currentSelectedId) {
        this.activityId = currentSelectedId
        console.log('使用Store中的selectedActivityId:', this.activityId);
      } else {
        console.log('没有找到任何activityId');
      }
    }

    // 检查用户是否绑定手机号
    this.checkMobileBinding()

    // 检查是否需要刷新（支付成功后返回）
    if (this.$route.query.refresh === 'true') {
      this.$toast.success('支付成功，账户已更新')
      // 清除URL中的refresh参数
      this.$router.replace({
        name: 'account',
        query: {
          activityId: this.activityId
        }
      })
    }
  },
  activated() {
    // 如果页面被缓存，在激活时检查状态同步
    console.log('Account页面被激活，检查活动状态同步');
    this.checkActivitySync();
  },
  methods: {
    // 检查手机号绑定状态
    checkMobileBinding() {
      console.log("开始检查用户手机号绑定状态");

      // 使用工具函数检查手机号，如果未绑定会自动跳转
      checkAndRedirectToBindMobile(window.location.href).then((hasMobile) => {
        if (hasMobile) {
          // 用户已绑定手机号，继续初始化活动数据
          console.log("用户已绑定手机号，继续加载页面数据");
          this.initActivity();
        }
        // 如果没有手机号，工具函数会自动处理跳转，这里不需要额外操作
      }).catch((error) => {
        console.error("检查手机号绑定状态失败:", error);
        // 即使检查失败，也尝试初始化活动数据（避免阻塞用户）
        this.initActivity();
      });
    },

    formatDate(date) {
      if (!date) return ''
      return dateUtil.formatDate.format(new Date(date), 'yyyy-MM-dd hh:mm')
    },

    // 初始化活动数据
    async initActivity() {
      console.log('开始初始化活动数据...', 'preferredActivityId:', this.activityId);

      try {
        // 只有当 this.activityId 是有效值且来自URL参数时才传递 preferredActivityId
        const initParams = {
          api: this.$fly,
          toast: this.$toast
        };

        // 只有当URL中明确传递了activityId时，才作为首选活动ID传递
        // 如果activityId来自Store（即从其他页面跳转过来），则不传递preferredActivityId，让系统使用缓存
        const urlActivityId = this.$route.query.activityId;
        if (urlActivityId && urlActivityId !== 'undefined' && urlActivityId !== null) {
          initParams.preferredActivityId = parseInt(urlActivityId);
          console.log('使用URL参数作为首选活动ID:', initParams.preferredActivityId);
        } else {
          console.log('不传递preferredActivityId，让系统使用缓存的活动ID');
        }

        const result = await this.$store.dispatch('activity/initializeActivity', initParams);

        if (result.success) {
          console.log('活动数据初始化成功:', result.data);
          // 更新本地的 activityId 为实际选中的活动ID
          this.activityId = this.selectedActivityId;
          console.log('设置activityId为:', this.activityId);
          // 初始化其他数据
          this.initializeData();
        } else {
          console.error('活动数据初始化失败:', result.error);
        }
      } catch (error) {
        console.error('初始化活动数据时发生错误:', error);
      }
    },

    // 初始化数据
    initializeData() {
      if (this.activityId) {
        console.log('开始初始化数据，activityId:', this.activityId);
        this.getAccountInfo();
        this.loadPackages();
        // 默认加载已支付的充值记录
        this.loadPaidRechargeRecords();
      } else {
        console.log('activityId为空，等待活动初始化完成');
      }
    },

    // 刷新记录数据
    refreshRecords() {
      if (this.isLoadingRecords) {
        console.log('记录正在加载中，跳过重复调用');
        return;
      }

      console.log('刷新记录数据');
      this.isLoadingRecords = true;

      // 重置所有充值记录状态
      this.paidRechargeList = [];
      this.paidRechargePage = 1;
      this.paidRechargeFinished = false;

      this.refundRechargeList = [];
      this.refundRechargePage = 1;
      this.refundRechargeFinished = false;

      this.allRechargeList = [];
      this.allRechargePage = 1;
      this.allRechargeFinished = false;

      // 兼容性：重置原有充值记录状态
      this.rechargeList = [];
      this.rechargePage = 1;
      this.rechargeFinished = false;

      // 根据当前选中的tab加载对应数据
      if (this.rechargeStatusTab === 'paid') {
        this.loadPaidRechargeRecords();
      } else if (this.rechargeStatusTab === 'refund') {
        this.loadRefundRechargeRecords();
      } else if (this.rechargeStatusTab === 'all') {
        this.loadAllRechargeRecords();
      } else {
        // 默认加载已支付记录
        this.loadPaidRechargeRecords();
      }

      // 重置使用记录状态
      this.usageList = [];
      this.usagePage = 1;
      this.usageFinished = false;
      // 只有当前在使用记录tab时才加载
      if (this.activeTab === 'usage') {
        this.loadUsageRecords();
      }

      // 延迟重置标志，避免快速重复调用
      setTimeout(() => {
        this.isLoadingRecords = false;
      }, 1000);
    },

    // 检查活动状态同步
    checkActivitySync() {
      // 如果全局状态中没有活动数据，重新初始化
      if (!this.hasActivities) {
        console.log('Account页面检测到没有活动数据，重新初始化');
        this.initActivity();
      } else if (this.currentActivity && this.activityId !== this.currentActivity.id) {
        // 如果当前页面的activityId与全局状态不一致，同步状态
        console.log('Account页面检测到活动状态不一致，同步状态');
        this.activityId = this.currentActivity.id;
        this.initializeData();
      }
    },

    // 活动切换
    async onActivityChange(value) {
      console.log('Activity changed to:', value);

      try {
        const result = await this.$store.dispatch('activity/switchActivity', value);
        if (result.success) {
          console.log('活动切换成功:', result.activity);
          this.activityId = value;

          // 重新获取账户信息
          this.getAccountInfo();

          // 刷新记录数据
          this.refreshRecords();
        } else {
          console.error('活动切换失败:', result.error);
          this.$toast.fail('切换活动失败');
        }
      } catch (error) {
        console.error('切换活动时发生错误:', error);
        this.$toast.fail('切换活动失败');
      }
    },
    
    // 获取账户信息
    getAccountInfo() {
      // 检查activityId是否存在
      if (!this.activityId) {
        console.log('activityId为空，跳过获取账户信息');
        return;
      }

      this.$fly.get('/pyp/web/account/info', {
        activityId: this.activityId
      }).then(res => {
        if (res.code === 200) {
          this.accountInfo = res.accountInfo
        } else {
          // 使用工具函数处理手机号相关错误
          if (!handleMobileError(res, window.location.href)) {
            console.error('获取账户信息失败:', res.msg)
          }
        }
      }).catch(err => {
        console.error('获取账户信息失败:', err)
      })
    },
    
    // 加载套餐数据
    loadPackages() {
      // 加载推荐套餐
      this.$fly.get('/pyp/web/account/packages/recommended').then(res => {
        if (res.code === 200) {
          this.recommendedPackages = res.packages || []
        }
      }).catch(err => {
        console.error('加载推荐套餐失败:', err)
      })

      // 加载充值次数套餐
      this.$fly.get('/pyp/web/account/packages/rechargeCount').then(res => {
        if (res.code === 200) {
          this.rechargeCountPackages = res.packages || []
        }
      }).catch(err => {
        console.error('加载充值次数套餐失败:', err)
      })

      // 加载创建活动套餐
      this.$fly.get('/pyp/web/account/packages/createActivity').then(res => {
        if (res.code === 200) {
          this.createActivityPackages = res.packages || []
        }
      }).catch(err => {
        console.error('加载创建活动套餐失败:', err)
      })
    },

    // 切换套餐显示
    togglePackages() {
      this.showPackages = !this.showPackages
    },

    // 选择套餐
    selectPackage(pkg) {
      if (pkg.packageType === 2) {
        // 创建活动套餐，需要输入活动名称
        this.showActivityNameDialog(pkg)
      } else {
        // 普通充值套餐，直接确认购买
        this.$dialog.confirm({
          title: '确认充值',
          message: `确定要购买 ${pkg.name} 吗？\n价格：¥${pkg.price}\n次数：${pkg.countValue}次`
        }).then(() => {
          this.createOrder(pkg)
        }).catch(() => {
          // 用户取消
        })
      }
    },

    // 显示活动名称输入对话框
    showActivityNameDialog(pkg) {
      this.selectedPackageForActivity = pkg
      this.activityNameInput = ''
      this.activityNameError = ''
      this.showActivityNameDialogVisible = true
    },

    // 验证活动名称
    validateActivityName() {
      const name = this.activityNameInput.trim()
      if (!name) {
        this.activityNameError = '活动名称不能为空'
        return false
      }
      if (name.length < 2) {
        this.activityNameError = '活动名称至少2个字符'
        return false
      }
      if (name.length > 50) {
        this.activityNameError = '活动名称不能超过50个字符'
        return false
      }
      this.activityNameError = ''
      return true
    },

    // 确认创建活动
    confirmCreateActivity() {
      if (this.validateActivityName()) {
        this.showActivityNameDialogVisible = false
        this.createActivityPackageOrder(this.selectedPackageForActivity, this.activityNameInput.trim())
      }
    },

    // 取消创建活动
    cancelCreateActivity() {
      this.showActivityNameDialogVisible = false
      this.selectedPackageForActivity = null
      this.activityNameInput = ''
      this.activityNameError = ''
    },

    // 创建普通充值订单
    createOrder(pkg) {
      const orderData = {
        packageId: pkg.id,
        activityId: this.activityId,
        rechargeType: pkg.packageType,
        appid: this.$cookie.get("appid"),
        repeatToken: this.generateToken()
      }

      this.$fly.post('/pyp/web/account/recharge', orderData).then(res => {
        if (res.code === 200) {
          this.$toast.success('订单创建成功')

          // 跳转到支付页面
          this.$router.push({
            name: 'rechargePayment',
            query: {
              orderId: res.orderId || res.result,
              from: 'account'
            }
          })
        } else {
          this.$toast.fail(res.msg || '订单创建失败')
        }
      }).catch(err => {
        console.error('创建订单失败:', err)
        this.$toast.fail('订单创建失败')
      })
    },

    // 创建活动套餐订单
    createActivityPackageOrder(pkg, activityName) {
      const orderData = {
        packageId: pkg.id,
        activityName: activityName,
        appid: this.$cookie.get("appid"),
        repeatToken: this.generateToken()
      }

      this.$fly.post('/pyp/web/account/createActivityPackage', orderData).then(res => {
        if (res.code === 200) {
          this.$toast.success('活动套餐订单创建成功')

          // 跳转到支付页面
          this.$router.push({
            name: 'rechargePayment',
            query: {
              orderId: res.orderId || res.result,
              from: 'account'
            }
          })
        } else {
          this.$toast.fail(res.msg || '订单创建失败')
        }
      }).catch(err => {
        console.error('创建活动套餐订单失败:', err)
        this.$toast.fail('订单创建失败')
      })
    },

    // 生成令牌
    generateToken() {
      return Math.random().toString(36).substring(2, 17) + Date.now().toString(36)
    },

    // 切换标签页
    handleTabClick(name) {
      // 确保activityId存在
      if (!this.activityId) {
        console.log('activityId为空，无法切换tab');
        return;
      }

      if (name === 'usage' && this.usageList.length === 0) {
        this.loadUsageRecords()
      }
    },

    // 切换充值记录状态tab
    handleRechargeStatusTabClick(name) {
      // 确保activityId存在
      if (!this.activityId) {
        console.log('activityId为空，无法切换tab');
        return;
      }

      if (name === 'paid' && this.paidRechargeList.length === 0) {
        this.loadPaidRechargeRecords()
      } else if (name === 'refund' && this.refundRechargeList.length === 0) {
        this.loadRefundRechargeRecords()
      } else if (name === 'all' && this.allRechargeList.length === 0) {
        this.loadAllRechargeRecords()
      }
    },
    
    // 加载已支付充值记录
    loadPaidRechargeRecords() {
      if (this.paidRechargeFinished) return

      // 检查activityId是否存在
      if (!this.activityId) {
        console.log('activityId为空，跳过加载已支付充值记录');
        return;
      }

      this.paidRechargeLoading = true
      console.log('加载已支付充值记录 - 页码:', this.paidRechargePage, '活动ID:', this.activityId);

      this.$fly.get('/pyp/web/account/records', {
        activityId: this.activityId,
        page: this.paidRechargePage,
        limit: 10,
        status: 1  // 已支付的记录
      }).then(res => {
        if (res.code === 200) {
          const newList = res.page.list || []
          console.log('获取到已支付充值记录:', newList.length, '条');

          // 去重处理
          const filteredList = newList.filter(newItem => {
            return !this.paidRechargeList.some(existingItem =>
              existingItem.id === newItem.id ||
              (existingItem.orderSn && newItem.orderSn && existingItem.orderSn === newItem.orderSn)
            );
          });

          this.paidRechargeList = this.paidRechargeList.concat(filteredList)

          if (newList.length < 10) {
            this.paidRechargeFinished = true
          } else {
            this.paidRechargePage++
          }
        } else {
          this.paidRechargeFinished = true
        }
        this.paidRechargeLoading = false
      }).catch(err => {
        console.error('加载已支付充值记录失败:', err)
        this.paidRechargeLoading = false
        this.paidRechargeFinished = true
      })
    },

    // 加载退款订单记录
    loadRefundRechargeRecords() {
      if (this.refundRechargeFinished) return

      // 检查activityId是否存在
      if (!this.activityId) {
        console.log('activityId为空，跳过加载退款订单记录');
        return;
      }

      this.refundRechargeLoading = true
      console.log('加载退款订单记录 - 页码:', this.refundRechargePage, '活动ID:', this.activityId);

      this.$fly.get('/pyp/web/account/records', {
        activityId: this.activityId,
        page: this.refundRechargePage,
        limit: 10,
        statusList: '2,3'  // 2-已取消, 3-已退款，用字符串传递
      }).then(res => {
        if (res.code === 200) {
          const newList = res.page.list || []
          console.log('获取到退款订单记录:', newList.length, '条');

          // 去重处理
          const filteredList = newList.filter(newItem => {
            return !this.refundRechargeList.some(existingItem =>
              existingItem.id === newItem.id ||
              (existingItem.orderSn && newItem.orderSn && existingItem.orderSn === newItem.orderSn)
            );
          });

          this.refundRechargeList = this.refundRechargeList.concat(filteredList)

          if (newList.length < 10) {
            this.refundRechargeFinished = true
          } else {
            this.refundRechargePage++
          }
        } else {
          this.refundRechargeFinished = true
        }
        this.refundRechargeLoading = false
      }).catch(err => {
        console.error('加载退款订单记录失败:', err)
        this.refundRechargeLoading = false
        this.refundRechargeFinished = true
      })
    },

    // 加载全部充值记录
    loadAllRechargeRecords() {
      if (this.allRechargeFinished) return

      // 检查activityId是否存在
      if (!this.activityId) {
        console.log('activityId为空，跳过加载全部充值记录');
        return;
      }

      this.allRechargeLoading = true
      console.log('加载全部充值记录 - 页码:', this.allRechargePage, '活动ID:', this.activityId);

      this.$fly.get('/pyp/web/account/records', {
        activityId: this.activityId,
        page: this.allRechargePage,
        limit: 10
        // 不传status参数，获取所有状态的记录
      }).then(res => {
        if (res.code === 200) {
          const newList = res.page.list || []
          console.log('获取到全部充值记录:', newList.length, '条');

          // 去重处理
          const filteredList = newList.filter(newItem => {
            return !this.allRechargeList.some(existingItem =>
              existingItem.id === newItem.id ||
              (existingItem.orderSn && newItem.orderSn && existingItem.orderSn === newItem.orderSn)
            );
          });

          this.allRechargeList = this.allRechargeList.concat(filteredList)

          if (newList.length < 10) {
            this.allRechargeFinished = true
          } else {
            this.allRechargePage++
          }
        } else {
          this.allRechargeFinished = true
        }
        this.allRechargeLoading = false
      }).catch(err => {
        console.error('加载全部充值记录失败:', err)
        this.allRechargeLoading = false
        this.allRechargeFinished = true
      })
    },

    // 加载充值记录（保持兼容性）
    loadRechargeRecords() {
      // 默认加载已支付的记录
      this.loadPaidRechargeRecords()
    },
    
    // 加载使用记录
    loadUsageRecords() {
      if (this.usageFinished) return

      // 检查activityId是否存在
      if (!this.activityId) {
        console.log('activityId为空，跳过加载使用记录');
        return;
      }

      this.usageLoading = true
      console.log('加载使用记录 - 页码:', this.usagePage, '活动ID:', this.activityId);

      this.$fly.get('/pyp/web/activity/recharge/usageRecords', {
        activityId: this.activityId,
        page: this.usagePage,
        limit: 10
      }).then(res => {
        if (res.code === 200) {
          const newList = res.page.list || []
          console.log('获取到使用记录:', newList.length, '条');

          // 去重处理 - 基于id去重
          const filteredList = newList.filter(newItem => {
            return !this.usageList.some(existingItem => existingItem.id === newItem.id);
          });

          console.log('去重后的记录:', filteredList.length, '条');
          this.usageList = this.usageList.concat(filteredList)

          if (newList.length < 10) {
            this.usageFinished = true
          } else {
            this.usagePage++
          }
        } else {
          this.usageFinished = true
        }
        this.usageLoading = false
      }).catch(err => {
        console.error('加载使用记录失败:', err)
        this.usageLoading = false
        this.usageFinished = true
      })
    },
    

    
    // 显示帮助信息
    showHelp() {
      this.$dialog.alert({
        title: '账户说明',
        message: '• 总次数：您购买的所有次数总和\n• 已使用：您已经使用的次数\n• 剩余次数：您还可以使用的次数\n\n充值记录显示您的所有充值历史，使用记录显示您的使用详情。'
      })
    },

    // 获取充值类型标签
    getRechargeTypeTag(type) {
      return this.rechargeTypeMap[type] || { text: '未知', type: 'default' }
    },

    // 获取充值状态标签
    getRechargeStatusTag(status) {
      return this.rechargeStatusMap[status] || { text: '未知', type: 'default' }
    },

    // 获取使用类型标签
    getUsageTypeTag(type) {
      return this.usageTypeMap[type] || { text: '未知', type: 'default' }
    },

    // 跳转到用户协议
    goToUserAgreement() {
      this.$router.push({ name: 'userAgreement' });
    },

    // 跳转到隐私政策
    goToPrivacyPolicy() {
      this.$router.push({ name: 'privacyPolicy' });
    },

    // 计算使用率百分比
    getUsagePercentage(item) {
      const total = item.countValue || 0
      const used = item.usedCount || 0
      if (total === 0) return 0
      return Math.round((used / total) * 100)
    },

    // 判断是否可以退款
    canRefund(item) {
      // 只有已支付状态且是用户自己充值的记录才能退款
      // rechargeType: 1-套餐充值, 2-自定义充值 (这两种是用户自己充值的，可以退款)
      // rechargeType: 3-系统赠送, 4-创建活动套餐, 5-活动续费套餐 (这些不能退款)
      // source: 1-用户充值(可退款), 2-系统赠送(不可退款), 3-活动奖励(不可退款)

      // 必须是已支付状态
      if (item.status !== 1) {
        return false
      }

      // 必须是用户自己充值的类型
      if (!(item.rechargeType === 1 || item.rechargeType === 2)) {
        return false
      }

      // 必须是用户充值来源（排除系统赠送和活动奖励）
      if (item.source && item.source !== 1) {
        return false
      }

      // 必须还有剩余次数
      const remainingCount = (item.countValue || 0) - (item.usedCount || 0)
      if (remainingCount <= 0) {
        return false
      }

      return true
    },

    // 获取退款按钮的样式类
    getRefundBtnClass(item) {
      const usedCount = item.usedCount || 0
      if (usedCount === 0) {
        return 'unused'  // 未使用 - 绿色
      } else {
        return 'partial-used'  // 部分使用 - 橙色
      }
    },

    // 处理退款
    handleRefund(item) {
      const usedCount = item.usedCount || 0
      const totalCount = item.countValue || 0
      const remainingCount = totalCount - usedCount

      // 如果已经使用了一些次数，需要确认
      if (usedCount > 0) {
        const originalAmount = item.payAmount || item.amount
        const refundRatio = (remainingCount / totalCount * 100).toFixed(1)
        const estimatedRefund = (originalAmount * remainingCount / totalCount).toFixed(2)

        this.$dialog.confirm({
          title: '💰 部分退款确认',
          message: `📊 使用情况：\n• 总次数：${totalCount}次\n• 已使用：${usedCount}次\n• 剩余：${remainingCount}次\n\n💵 退款信息：\n• 原支付金额：¥${originalAmount}\n• 可退款比例：${refundRatio}%\n• 预计退款：¥${estimatedRefund}\n\n确认申请退款吗？`,
          confirmButtonText: '确认退款',
          cancelButtonText: '再想想',
          confirmButtonColor: '#ff6b6b'
        }).then(() => {
          this.submitRefund(item)
        }).catch(() => {
          // 用户取消
        })
      } else {
        // 未使用，直接退款
        this.$dialog.confirm({
          title: '💚 全额退款确认',
          message: `🎯 订单信息：\n• 订单号：${item.orderSn}\n• 充值次数：${totalCount}次\n• 使用情况：未使用\n\n💵 退款金额：¥${item.payAmount || item.amount}\n\n确认申请全额退款吗？`,
          confirmButtonText: '确认退款',
          cancelButtonText: '再想想',
          confirmButtonColor: '#4caf50'
        }).then(() => {
          this.submitRefund(item)
        }).catch(() => {
          // 用户取消
        })
      }
    },

    // 提交退款请求
    submitRefund(item) {
      this.$toast.loading({
        message: '退款申请中...',
        forbidClick: true,
        duration: 0
      })

      this.$fly.post('/pyp/web/account/refund', {
        rechargeRecordId: item.id,
        orderSn: item.orderSn
      }).then(res => {
        this.$toast.clear()
        if (res.code === 200) {
          // 显示成功信息，包含退款金额
          const refundAmount = res.refundAmount || '计算中'
          this.$toast.success({
            message: `✅ 退款申请已提交\n预计退款：¥${refundAmount}`,
            duration: 3000
          })
          // 刷新充值记录
          this.refreshRecords()
        } else {
          this.$toast.fail(res.msg || '退款申请失败')
        }
      }).catch(err => {
        this.$toast.clear()
        console.error('退款申请失败:', err)
        this.$toast.fail('❌ 退款申请失败，请稍后重试')
      })
    },

    // 去支付
    goToPay(item) {
      this.$router.push({
        name: 'rechargePayment',
        query: {
          orderId: item.id || item.orderSn,
          from: 'account'
        }
      })
    },

    // 刷新记录（最终版本）
    refreshRecords() {
      // 重置所有分页状态
      this.paidRechargePage = 1
      this.refundRechargePage = 1
      this.allRechargePage = 1
      this.usagePage = 1

      this.paidRechargeFinished = false
      this.refundRechargeFinished = false
      this.allRechargeFinished = false
      this.usageFinished = false

      // 清空所有列表
      this.paidRechargeList = []
      this.refundRechargeList = []
      this.allRechargeList = []
      this.usageList = []

      // 重新加载当前tab的数据
      if (this.rechargeStatusTab === 'paid') {
        this.loadPaidRechargeRecords()
      } else if (this.rechargeStatusTab === 'refund') {
        this.loadRefundRechargeRecords()
      } else if (this.rechargeStatusTab === 'all') {
        this.loadAllRechargeRecords()
      }

      if (this.activeTab === 'usage') {
        this.loadUsageRecords()
      }
    }
  }
}
</script>

<style scoped>
.account-page {
  padding: 0 0 20px 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.activity-selector {
  background: white;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;

  .activity-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 20px;
  margin: 0 20px 20px 20px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.balance-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
}

.balance-content {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.balance-item {
  text-align: center;
}

.balance-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.balance-label {
  font-size: 14px;
  opacity: 0.9;
}

.balance-actions {
  text-align: center;
}

.packages-section {
  background: white;
  border-radius: 10px;
  padding: 15px;
  margin: 0 20px 20px 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.package-group {
  margin-bottom: 20px;
}

.package-group h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #666;
}

.package-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 10px;
}

.package-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.package-item:hover {
  border-color: #1989fa;
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.2);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.package-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.package-price {
  font-size: 18px;
  font-weight: bold;
  color: #ff4444;
}

.package-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-count {
  font-size: 14px;
  color: #1989fa;
  font-weight: bold;
}

.package-desc {
  font-size: 12px;
  color: #999;
  flex: 1;
  margin-left: 10px;
}

.record-tabs {
  /* background: white; */
  border-radius: 10px;
  overflow: hidden;
  margin: 0 20px;
}
::v-deep .van-tabs__content {
  margin-top: 10px;
}
/* 状态子tab样式 */
.status-sub-tabs {
  background: white;
  border-radius: 8px;
  margin-top: 10px;
}

::v-deep .status-sub-tabs .van-tabs__nav {
  background: #f8f9fa;
  border-radius: 6px;
  margin: 10px;
  padding: 2px;
}

::v-deep .status-sub-tabs .van-tab {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

::v-deep .status-sub-tabs .van-tab--active {
  background: white;
  color: #1989fa;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

::v-deep .status-sub-tabs .van-tabs__line {
  display: none;
}

.record-list {
  padding: 0 10px;
}

.record-item {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.record-amount {
  font-size: 16px;
  font-weight: bold;
  color: #1989fa;
}

.record-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-type {
  flex: 1;
}

.record-price {
  font-size: 14px;
  color: #ff4444;
  font-weight: bold;
}

.record-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.record-usage {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0 6px 0;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  font-size: 12px;
}

.usage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.usage-label {
  color: #666;
  margin-bottom: 2px;
}

.usage-value {
  font-weight: bold;
  font-size: 13px;
}

.usage-value.used {
  color: #f56c6c;
}

.usage-value.remaining {
  color: #67c23a;
}

.usage-value.rate {
  color: #409eff;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.record-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.refund-btn {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  padding: 2px 8px;
  background: #ff4757;
  color: white;
  border-radius: 2px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  font-weight: 500;
  min-height: 20px;
  white-space: nowrap;
  line-height: 1;
}

.refund-btn:hover {
  background: #ff3742;
  opacity: 0.9;
}

.refund-btn:active {
  background: #ff2f3a;
  opacity: 0.8;
}

.refund-btn .van-icon {
  font-size: 12px;
}

.refund-btn span {
  font-size: 12px;
  line-height: 1;
}

.status-tag {
  font-weight: 500;
}

/* 为不同状态添加不同的退款按钮样式 */
.refund-btn.partial-used {
  background: #ff9500;
}

.refund-btn.partial-used:hover {
  background: #e6850e;
}

.refund-btn.unused {
  background: #2ed573;
}

.refund-btn.unused:hover {
  background: #26c065;
}

/* 状态标签样式优化 */
.status-tag {
  font-weight: 500;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 2px;
  line-height: 1;
  min-height: 20px;
  display: inline-flex;
  align-items: center;
}

.record-time {
  font-size: 12px;
  color: #999;
}

.record-status {
  font-size: 12px;
}

.record-activity {
  margin-top: 8px;
  font-size: 12px;
  color: #1989fa;
  background: #f0f8ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.settings-section {
  background: white;
  border-radius: 10px;
  margin: 20px 20px 0 20px;
  overflow: hidden;
}

.settings-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.settings-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.settings-list {
  padding: 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.setting-item:hover {
  background-color: #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-content {
  display: flex;
  align-items: center;
}

.setting-content .van-icon {
  margin-right: 12px;
  color: #1989fa;
  font-size: 18px;
}

.setting-text {
  font-size: 16px;
  color: #333;
}

.setting-item .van-icon[name="arrow"] {
  color: #c8c9cc;
  font-size: 14px;
}

/* 活动名称输入对话框样式 */
.activity-name-dialog {
  padding: 20px;
}

.package-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.package-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #333;
}

.input-section {
  margin-top: 10px;
}
</style>
