<template>
  <div class="notification-list">
    <!-- 通知列表 -->
    <div v-if="notifications.length > 0" class="notification-container">
      <div 
        v-for="notification in notifications" 
        :key="notification.id"
        class="notification-item"
        :class="{ 'unread': notification.read === 0 }"
        @click="handleNotificationClick(notification)"
      >
        <div class="notification-icon">
          <van-icon 
            :name="getNotificationIcon(notification.type)" 
            :color="getNotificationColor(notification.type)"
            size="20px"
          />
        </div>
        
        <div class="notification-content">
          <div class="notification-title">
            {{ getNotificationTitle(notification.type) }}
          </div>
          <div class="notification-message">
            {{ notification.name }}
          </div>
          <div class="notification-time">
            {{ formatTime(notification.createOn) }}
          </div>
        </div>
        
        <div v-if="notification.read === 0" class="unread-dot"></div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <van-empty description="暂无通知" />
    </div>
    
    <!-- 加载更多 -->
    <div v-if="hasMore && notifications.length > 0" class="load-more">
      <van-button 
        type="default" 
        size="small" 
        :loading="loading"
        @click="loadMore"
      >
        {{ loading ? '加载中...' : '加载更多' }}
      </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActivityNotificationList',
  props: {
    activityId: {
      type: [String, Number],
      required: true
    },
    // 是否只显示过期相关通知
    expirationOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      notifications: [],
      loading: false,
      hasMore: true,
      page: 1,
      pageSize: 10
    };
  },
  mounted() {
    this.loadNotifications();
  },
  methods: {
    async loadNotifications(isLoadMore = false) {
      if (this.loading) return;
      
      this.loading = true;
      try {
        const params = {
          activityId: this.activityId,
          page: isLoadMore ? this.page + 1 : 1,
          limit: this.pageSize
        };
        
        // 如果只显示过期相关通知，添加类型过滤
        if (this.expirationOnly) {
          params.types = '7,8,9'; // 7-即将过期，8-已过期，9-续费成功
        }
        
        const response = await this.$fly.get('/activity/activitynotify/list', { params });
        
        if (response.data.code === 200) {
          const newNotifications = response.data.data || [];
          
          if (isLoadMore) {
            this.notifications = [...this.notifications, ...newNotifications];
            this.page++;
          } else {
            this.notifications = newNotifications;
            this.page = 1;
          }
          
          this.hasMore = newNotifications.length === this.pageSize;
        } else {
          this.$toast.fail(response.data.msg || '获取通知失败');
        }
      } catch (error) {
        console.error('获取通知失败:', error);
        this.$toast.fail('获取通知失败');
      } finally {
        this.loading = false;
      }
    },
    
    loadMore() {
      this.loadNotifications(true);
    },
    
    async handleNotificationClick(notification) {
      // 标记为已读
      if (notification.read === 0) {
        await this.markAsRead(notification.id);
        notification.read = 1;
      }
      
      // 处理点击事件
      if (notification.url) {
        // 如果有URL，跳转到对应页面
        if (notification.url.startsWith('/')) {
          this.$router.push(notification.url);
        } else {
          window.open(notification.url, '_blank');
        }
      } else {
        // 根据通知类型执行相应操作
        this.handleNotificationAction(notification);
      }
      
      // 触发点击事件
      this.$emit('notification-click', notification);
    },
    
    async markAsRead(notificationId) {
      try {
        await this.$fly.post('/activity/activitynotify/read', {
          id: notificationId.toString()
        });
      } catch (error) {
        console.error('标记已读失败:', error);
      }
    },
    
    handleNotificationAction(notification) {
      switch (notification.type) {
        case 7: // 即将过期
        case 8: // 已过期
          // 跳转到续费页面
          this.$emit('show-renewal', notification.activityId);
          break;
        case 9: // 续费成功
          // 跳转到活动详情
          this.$router.push(`/activity/detail?id=${notification.activityId}`);
          break;
        default:
          break;
      }
    },
    
    getNotificationIcon(type) {
      const iconMap = {
        7: 'clock-o',      // 即将过期
        8: 'warning-o',    // 已过期
        9: 'success',      // 续费成功
        default: 'bell-o'  // 默认
      };
      return iconMap[type] || iconMap.default;
    },
    
    getNotificationColor(type) {
      const colorMap = {
        7: '#ff9500',      // 即将过期 - 橙色
        8: '#ee0a24',      // 已过期 - 红色
        9: '#07c160',      // 续费成功 - 绿色
        default: '#969799' // 默认 - 灰色
      };
      return colorMap[type] || colorMap.default;
    },
    
    getNotificationTitle(type) {
      const titleMap = {
        7: '过期提醒',
        8: '过期通知',
        9: '续费成功',
        default: '系统通知'
      };
      return titleMap[type] || titleMap.default;
    },
    
    formatTime(time) {
      if (!time) return '';
      const date = new Date(time);
      const now = new Date();
      const diffInHours = (now - date) / (1000 * 60 * 60);
      
      if (diffInHours < 1) {
        return '刚刚';
      } else if (diffInHours < 24) {
        return `${Math.floor(diffInHours)}小时前`;
      } else if (diffInHours < 24 * 7) {
        return `${Math.floor(diffInHours / 24)}天前`;
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    },
    
    // 刷新通知列表
    refresh() {
      this.loadNotifications();
    }
  }
};
</script>

<style scoped>
.notification-list {
  width: 100%;
}

.notification-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f7f8fa;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #f7f8fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 13px;
  color: #646566;
  line-height: 1.4;
  margin-bottom: 8px;
  word-break: break-word;
}

.notification-time {
  font-size: 12px;
  color: #969799;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background-color: #ee0a24;
  border-radius: 50%;
  margin-left: 8px;
  margin-top: 8px;
  flex-shrink: 0;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.load-more {
  padding: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .notification-item {
    padding: 12px;
  }
  
  .notification-title {
    font-size: 13px;
  }
  
  .notification-message {
    font-size: 12px;
  }
  
  .notification-time {
    font-size: 11px;
  }
}
</style>
