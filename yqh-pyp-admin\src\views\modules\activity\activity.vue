<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="名称" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item v-if="appid == 'wx0770d56458b33c67'">
        <el-select v-model="dataForm.clientId" filterable>
          <el-option :value="''" label="全部(客户)"></el-option>
          <el-option v-for="item in client" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('activity:activity:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="success" @click="showMerchantQrcode()">商家登录二维码</el-button>
        <el-button v-if="isAuth('activity:activity:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" height="800px" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="活动名称">
      </el-table-column>
      <el-table-column prop="pvCount" header-align="center" align="center" label="点击数">
      </el-table-column>
      <el-table-column prop="uvCount" header-align="center" align="center" label="访问数">
      </el-table-column>
      <el-table-column prop="expirationTime" header-align="center" align="center" label="过期时间" width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.expirationTime">
            {{ formatDate(scope.row.expirationTime) }}
          </span>
          <span v-else style="color: #67C23A;">永不过期</span>
        </template>
      </el-table-column>
      <el-table-column prop="isExpired" header-align="center" align="center" label="过期状态" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isExpired === 1" type="danger">已过期</el-tag>
          <el-tag v-else-if="isExpiringSoon(scope.row)" type="warning">即将过期</el-tag>
          <el-tag v-else type="success">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="pvCount"   width="150px" header-align="center" align="center" label="活动网站">
        <div slot-scope="scope"  @click="openUrl('https://yqihua.com/p_front/#/cms/index?id=' + scope.row.id)">
          <vue-qrcode :options="{ width: 120 }" :value="'https://yqihua.com/p_front/#/cms/index?id=' + scope.row.id"></vue-qrcode>
        </div>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="350" label="操作">
        <template slot-scope="scope">
            <!-- <el-button type="text" size="small" v-if="isAuth('activity:activity:copy')" @click="copy(scope.row.id)">复制新活动</el-button> -->
            <el-button type="text" size="small" @click="acitivityConfig(scope.row.id)">活动配置</el-button>
            <el-button v-if="isAuth('activity:activity:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
            <el-button type="text" size="small" @click="manageExpiration(scope.row)">过期管理</el-button>
            <el-button v-if="isAuth('activity:activity:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->

    <!-- 商家登录二维码弹窗 -->
    <el-dialog title="商家登录二维码" :visible.sync="merchantQrcodeVisible" width="450px" center>
      <div style="text-align: center;">
        <canvas ref="merchantQrcodeCanvas" width="300" height="350"
          style="border: 1px solid #ddd; border-radius: 8px;"></canvas>
        <p style="margin-top: 20px; color: #666;">扫描二维码进入商家登录页面</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyMerchantQrcodeImage">复制图片</el-button>
        <el-button type="primary" @click="merchantQrcodeVisible = false">确定</el-button>
      </span>
    </el-dialog>

    <!-- 过期管理弹窗 -->
    <el-dialog title="活动过期管理" :visible.sync="expirationManageVisible" width="600px">
      <div v-if="currentActivity">
        <el-form :model="expirationForm" label-width="120px">
          <el-form-item label="活动名称">
            <span>{{ currentActivity.name }}</span>
          </el-form-item>
          <el-form-item label="当前状态">
            <el-tag v-if="currentActivity.isExpired === 1" type="danger">已过期</el-tag>
            <el-tag v-else-if="isExpiringSoon(currentActivity)" type="warning">即将过期</el-tag>
            <el-tag v-else type="success">正常</el-tag>
          </el-form-item>
          <el-form-item label="当前过期时间">
            <span v-if="currentActivity.expirationTime">
              {{ formatDate(currentActivity.expirationTime) }}
            </span>
            <span v-else style="color: #67C23A;">永不过期</span>
          </el-form-item>
          <el-form-item label="设置过期时间">
            <el-date-picker
              v-model="expirationForm.expirationTime"
              type="datetime"
              placeholder="选择过期时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
            <el-button type="text" @click="expirationForm.expirationTime = null">设为永不过期</el-button>
          </el-form-item>
          <el-form-item label="或延长天数">
            <el-input-number v-model="expirationForm.extendDays" :min="1" :max="3650" placeholder="延长天数"></el-input-number>
            <span style="margin-left: 10px;">天</span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="expirationManageVisible = false">取消</el-button>
        <el-button type="primary" @click="setExpirationTime" :loading="expirationLoading">设置过期时间</el-button>
        <el-button type="success" @click="extendExpiration" :loading="expirationLoading">延长有效期</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
import { yesOrNo } from "@/data/common"
  export default {
    data() {
      return {
        client: [],
        appid: '',
        wxAccount: {},
        yesOrNo: yesOrNo,
        dataForm: {
          name: '',
          clientId: '',
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        merchantQrcodeVisible: false,
        merchantLoginUrl: 'https://yqihua.com/p_front/#/',
        expirationManageVisible: false,
        expirationLoading: false,
        currentActivity: null,
        expirationForm: {
          expirationTime: null,
          extendDays: 30
        }
      }
    },
    components: {
      VueQrcode
    },
    activated() {
    this.appid = this.$cookie.get("appid");
      this.getDataList()
      this.getAccountInfo()
      this.findClient()
    },
    methods: {
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/activity/activity/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'clientId': this.dataForm.clientId,
            'name': this.dataForm.name
          })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
    findClient() {
      this.$http({
        url: this.$http.adornUrl("/client/client/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.client = data.result;
          }
        })
    },
    getAccountInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/manage/wxAccount/info/${this.appid}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxAccount = data.wxAccount;
        }
      });
    },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle(val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
        this.$router.push({
          name: 'activityAddOrUpdate',
          query: {
            id: id
          }
        })
        // this.$nextTick(() => {
        //   this.$refs.addOrUpdate.init(id)
        // })
      },
      // 删除
      deleteHandle(id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/activity/activity/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      copy(id) {
        this.$confirm(`确定复制会议操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/activity/activity/copy'),
            method: 'get',
            params: this.$http.adornParams({id: id})
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1000,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      acitivityConfig(v) {
        this.$router.push({
          name: 'activityConfig',
          query: {
            activityId: v
          }
        })
      },
      openUrl(v) {
        window.open(v)
      },

      // 显示商家登录二维码
      showMerchantQrcode() {
        this.merchantQrcodeVisible = true
        this.$nextTick(() => {
          this.generateMerchantQrcodeCanvas()
        })
      },

      // 生成canvas二维码
      async generateMerchantQrcodeCanvas() {
        const canvas = this.$refs.merchantQrcodeCanvas
        const ctx = canvas.getContext('2d')

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // 设置背景色为白色
        ctx.fillStyle = '#ffffff'
        ctx.fillRect(0, 0, canvas.width, canvas.height)

        try {
          // 使用本地qrcode库生成二维码
          const QRCode = require('qrcode')

          // 生成二维码数据URL
          const qrcodeDataURL = await QRCode.toDataURL(this.merchantLoginUrl, {
            width: 200,
            height: 200,
            margin: 1,
            color: {
              dark: '#000000',
              light: '#FFFFFF'
            }
          })

          // 创建二维码图片
          const qrcodeImg = new Image()
          qrcodeImg.onload = () => {
            // 绘制二维码，居中显示
            const qrcodeSize = 200
            const qrcodeX = (canvas.width - qrcodeSize) / 2
            const qrcodeY = 30
            ctx.drawImage(qrcodeImg, qrcodeX, qrcodeY, qrcodeSize, qrcodeSize)

            // 绘制文字
            ctx.fillStyle = '#333333'
            ctx.font = 'bold 16px Arial, sans-serif'
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'

            const textY = qrcodeY + qrcodeSize + 40
            ctx.fillText('易企化AI爆店码商家端', canvas.width / 2, textY)
          }

          qrcodeImg.onerror = () => {
            this.drawErrorMessage(ctx, canvas, '二维码图片加载失败')
          }

          qrcodeImg.src = qrcodeDataURL
        } catch (error) {
          console.error('生成二维码失败:', error)
          this.drawErrorMessage(ctx, canvas, '二维码生成失败')
        }
      },

      // 绘制错误信息
      drawErrorMessage(ctx, canvas, message) {
        ctx.fillStyle = '#ff0000'
        ctx.font = '16px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(message, canvas.width / 2, canvas.height / 2)
      },

      // 复制商家登录二维码图片
      copyMerchantQrcodeImage() {
        const canvas = this.$refs.merchantQrcodeCanvas

        // 将canvas转换为blob
        canvas.toBlob((blob) => {
          if (navigator.clipboard && window.ClipboardItem) {
            // 使用现代剪贴板API
            const item = new ClipboardItem({ 'image/png': blob })
            navigator.clipboard.write([item]).then(() => {
              this.$message.success('图片已复制到剪贴板')
            }).catch(() => {
              this.fallbackCopyMerchantImage(canvas)
            })
          } else {
            this.fallbackCopyMerchantImage(canvas)
          }
        }, 'image/png')
      },

      // 备用复制方法
      fallbackCopyMerchantImage(canvas) {
        try {
          // 创建一个临时的图片元素
          const dataURL = canvas.toDataURL('image/png')
          const link = document.createElement('a')
          link.download = '易企化商家登录二维码.png'
          link.href = dataURL
          link.click()
          this.$message.success('图片已下载到本地')
        } catch (error) {
          this.$message.error('复制失败，请手动截图保存')
        }
      },

      // 过期管理
      manageExpiration(activity) {
        this.currentActivity = activity
        this.expirationForm.expirationTime = activity.expirationTime
        this.expirationForm.extendDays = 30
        this.expirationManageVisible = true
      },

      // 设置过期时间
      setExpirationTime() {
        if (!this.currentActivity) return

        this.expirationLoading = true
        this.$http({
          url: this.$http.adornUrl('/activity/expiration/setExpirationTime'),
          method: 'post',
          data: this.$http.adornData({
            activityId: this.currentActivity.id,
            expirationTime: this.expirationForm.expirationTime ? new Date(this.expirationForm.expirationTime).getTime() : null
          })
        }).then(({ data }) => {
          this.expirationLoading = false
          if (data && data.code === 200) {
            this.$message.success('设置成功')
            this.expirationManageVisible = false
            this.getDataList()
          } else {
            this.$message.error(data.msg || '设置失败')
          }
        }).catch(() => {
          this.expirationLoading = false
          this.$message.error('设置失败')
        })
      },

      // 延长有效期
      extendExpiration() {
        if (!this.currentActivity || !this.expirationForm.extendDays) {
          this.$message.warning('请输入延长天数')
          return
        }

        this.expirationLoading = true
        this.$http({
          url: this.$http.adornUrl('/activity/expiration/extend'),
          method: 'post',
          data: this.$http.adornData({
            activityId: this.currentActivity.id,
            days: this.expirationForm.extendDays
          })
        }).then(({ data }) => {
          this.expirationLoading = false
          if (data && data.code === 200) {
            this.$message.success('延长成功')
            this.expirationManageVisible = false
            this.getDataList()
          } else {
            this.$message.error(data.msg || '延长失败')
          }
        }).catch(() => {
          this.expirationLoading = false
          this.$message.error('延长失败')
        })
      },

      // 格式化日期
      formatDate(date) {
        if (!date) return ''
        const d = new Date(date)
        return d.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      },

      // 判断是否即将过期（7天内）
      isExpiringSoon(activity) {
        if (!activity.expirationTime || activity.isExpired === 1) return false
        const now = new Date()
        const expiration = new Date(activity.expirationTime)
        const diffTime = expiration.getTime() - now.getTime()
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        return diffDays <= 7 && diffDays > 0
      }
    }
  }
</script>
