package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.activity.dao.AdTypeConfigDao;
import com.cjy.pyp.modules.activity.entity.AdTypeConfigEntity;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

@Service("adTypeConfigService")
public class AdTypeConfigServiceImpl extends ServiceImpl<AdTypeConfigDao, AdTypeConfigEntity> implements AdTypeConfigService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        
        // 按类型名称搜索
        String typeName = (String) params.get("typeName");
        if (StringUtils.hasText(typeName)) {
            wrapper.like("type_name", typeName);
        }
        
        // 按状态过滤
        String status = (String) params.get("status");
        if (StringUtils.hasText(status)) {
            wrapper.eq("status", status);
        }
        
        // 按排序和创建时间排序
        wrapper.orderByAsc("sort_order").orderByDesc("create_on");

        IPage<AdTypeConfigEntity> page = this.page(
                new Query<AdTypeConfigEntity>().getPage(params),
                wrapper
        );

        return new PageUtils(page);
    }
    
    @Override
    public List<AdTypeConfigEntity> getEnabledConfigs() {
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);
        wrapper.orderByAsc("sort_order");
        return this.list(wrapper);
    }
    
    @Override
    public AdTypeConfigEntity getByTypeCode(String typeCode) {
        if (!StringUtils.hasText(typeCode)) {
            return null;
        }
        
        QueryWrapper<AdTypeConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("type_code", typeCode);
        wrapper.eq("status", 1);
        return this.getOne(wrapper);
    }
    
    @Override
    public String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle) {
        return buildPrompt(typeCode, keyword, nameMode, manualTitle, null);
    }

    @Override
    public String buildPrompt(String typeCode, String keyword, String nameMode, String manualTitle, String userCustomInput) {
        AdTypeConfigEntity config = getByTypeCode(typeCode);
        if (config == null) {
            // 如果找不到配置，使用通用配置
            config = getByTypeCode("general");
        }

        if (config == null) {
            throw new RuntimeException("未找到广告类型配置: " + typeCode);
        }

        String promptTemplate = config.getPromptTemplate();

        // 替换模板中的占位符
        String prompt = promptTemplate
                .replace("{platform}", config.getPlatform())
                .replace("{content_type}", config.getContentType())
                .replace("{keyword}", keyword != null ? keyword : "")
                .replace("{title_length}", config.getTitleLength())
                .replace("{content_length}", config.getContentLength())
                .replace("{topics_count}", String.valueOf(config.getTopicsCount()))
                .replace("{topics_format}", config.getTopicsFormat())
                .replace("{requirements}", config.getRequirements())
                .replace("{style}", config.getStyle());

        // 处理标题部分
        String titleSection = "";
        if ("manual".equals(nameMode) && StringUtils.hasText(manualTitle)) {
            titleSection = "标题：" + manualTitle + "\n";
        }
        prompt = prompt.replace("{title_section}", titleSection);

        // 处理用户自定义输入部分
        String userInputSection = "";
        if (StringUtils.hasText(userCustomInput)) {
            userInputSection = "\n\n用户补充要求：" + userCustomInput;
        }
        prompt = prompt + userInputSection;

        return prompt;
    }
}
