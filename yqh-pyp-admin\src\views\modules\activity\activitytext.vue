<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="关键词" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.adType" placeholder="广告类型" clearable style="width: 150px;">
          <el-option
            v-for="item in adTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('activity:activitytext:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activitytext:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <!-- 生成文案区域 -->
    <el-card class="generate-content-card" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">AI文案生成</span>
        <!-- <span style="float: right; font-size: 12px; color: #909399;">
          已加载活动默认配置
        </span> -->
      </div>
      <el-form :model="generateForm" label-width="100px">
        <!-- 广告类型选择 -->
        <el-form-item label="广告类型：">
          <el-select v-model="generateForm.adType" placeholder="请选择广告类型" style="width: 400px;">
            <el-option
              v-for="item in adTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.description }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 标题生成方式选择 -->
        <el-form-item label="标题生成：">
          <el-radio-group v-model="generateForm.nameMode">
            <el-radio label="ai">AI生成</el-radio>
            <el-radio label="manual">手动填写</el-radio>
          </el-radio-group>
          <!-- <div style="margin-top: 5px; font-size: 12px; color: #909399;">
            <i class="el-icon-info"></i>
            活动默认模式：{{ activityConfig.nameMode === 'ai' ? 'AI生成' : '手动填写' }}
          </div> -->
        </el-form-item>

        <!-- 手动填写标题 -->
        <el-form-item v-if="generateForm.nameMode === 'manual'" label="标题：">
          <el-input v-model="generateForm.manualTitle"
            :placeholder="activityConfig.defaultName ? `默认：${activityConfig.defaultName}` : '请输入标题（20字以内）'"
            maxlength="20" show-word-limit style="width: 400px;">
          </el-input>
          <!-- <div v-if="activityConfig.defaultName" style="margin-top: 5px; font-size: 12px; color: #909399;">
            <i class="el-icon-info"></i>
            活动默认标题：{{ activityConfig.defaultName }}
          </div> -->
        </el-form-item>

        <!-- 提示词输入 -->
        <el-form-item label="提示词：">
          <el-input v-model="generateForm.promptKeyword"
            :placeholder="activityConfig.defaultTitle ? `默认：${activityConfig.defaultTitle}` : '请输入提示词，如：易企化,碰一碰AI爆店码,获客营销'"
            style="width: 400px;">
          </el-input>
          <!-- <div v-if="activityConfig.defaultTitle" style="margin-top: 5px; font-size: 12px; color: #909399;">
            <i class="el-icon-info"></i>
            活动默认提示词：{{ activityConfig.defaultTitle }}
          </div> -->
        </el-form-item>

        <!-- 用户自定义补充 -->
        <el-form-item label="自定义补充：">
          <el-input type="textarea" v-model="generateForm.userCustomInput"
            :placeholder="activityConfig.defaultUserInput || '可以在这里补充您的想法或特殊要求，比如：突出产品特色、针对特定人群、特定风格等'"
            :rows="3" style="width: 600px;">
          </el-input>
          <div style="margin-top: 5px; font-size: 12px; color: #909399;">
            <i class="el-icon-info"></i>
            这里的内容会和上面的提示词一起组装发送给AI
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="generateContent" :loading="generating">
            {{ generating ? '生成中...' : '生成文案' }}
          </el-button>
          <el-button type="info" @click="resetToDefault" style="margin-left: 10px;">
            重置为默认配置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 生成结果显示 -->
      <div v-if="generatedResult"
        style="margin-top: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h4 style="margin-top: 0; color: #409EFF;">生成结果：</h4>
        <div style="white-space: pre-line; line-height: 1.6;">{{ formattedResult }}</div>
      </div>
    </el-card>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="model" header-align="center" align="center" label="使用模型">
      </el-table-column>
      <el-table-column prop="adType" header-align="center" align="center" label="广告类型">
        <template slot-scope="scope">
          <el-tag :type="getAdTypeTagType(scope.row.adType)">
            {{ getAdTypeLabel(scope.row.adType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="prompt" :show-overflow-tooltip="true" header-align="center" align="center" label="提示词">
      </el-table-column>
      <!-- <el-table-column prop="resultSummary" header-align="center" align="center" label="结果摘要">
      </el-table-column>
      <el-table-column prop="searchResults" header-align="center" align="center" label="搜索结果(JSON格式)">
      </el-table-column>
      <el-table-column prop="thinkProcess" header-align="center" align="center" label="AI思考过程">
      </el-table-column>
      <el-table-column prop="query" header-align="center" align="center" label="自定义输入">
      </el-table-column> -->
      <el-table-column prop="name" header-align="center" align="center" label="标题">
      </el-table-column>
      <el-table-column prop="title" header-align="center" align="center" label="提示词">
      </el-table-column>
      <el-table-column prop="result" :show-overflow-tooltip="true" header-align="center" align="center" label="文案">
      </el-table-column>
      <el-table-column prop="useCount" header-align="center" align="center" label="使用次数">
      </el-table-column>
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './activitytext-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        name: '',
        appid: '',
        adType: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      activityId: '',
      // 生成文案相关数据
      generateForm: {
        adType: 'douyin', // 广告类型
        nameMode: 'ai', // ai: AI生成, manual: 手动填写
        manualTitle: '', // 手动填写的标题
        promptKeyword: '易企化,碰一碰AI爆店码,获客营销', // 提示词
        userCustomInput: '' // 用户自定义输入
      },
      // 活动默认配置
      activityConfig: {
        nameMode: 'ai',
        defaultName: '',
        defaultTitle: '',
        defaultUserInput: ''
      },
      generating: false,
      generatedResult: null,
      formattedResult: '',
      // 广告类型选项
      adTypeOptions: []
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.activityId = this.$route.query.activityId
    this.loadActivityConfig()
    this.loadAdTypeConfigs()
    this.getDataList()
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },

    // 加载活动配置
    loadActivityConfig() {
      if (!this.activityId) {
        console.warn('活动ID为空，无法加载活动配置');
        return;
      }

      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.activityId}`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          const activity = data.activity;
          this.activityConfig = {
            nameMode: activity.nameMode || 'ai',
            defaultName: activity.defaultName || '',
            defaultTitle: activity.defaultTitle || '',
            defaultUserInput: activity.defaultUserInput || ''
          };

          // 应用活动配置到生成表单
          this.applyActivityConfig();
        } else {
          console.error('加载活动配置失败:', data.msg);
        }
      }).catch(error => {
        console.error('加载活动配置失败:', error);
      });
    },

    // 应用活动配置到生成表单
    applyActivityConfig() {
      // 设置标题生成模式
      this.generateForm.nameMode = this.activityConfig.nameMode;

      // 如果是手动填写模式且有默认标题，则使用默认标题
      if (this.activityConfig.nameMode === 'manual' && this.activityConfig.defaultName) {
        this.generateForm.manualTitle = this.activityConfig.defaultName;
      }

      // 如果有默认提示词，则使用默认提示词
      if (this.activityConfig.defaultTitle) {
        this.generateForm.promptKeyword = this.activityConfig.defaultTitle;
      }

      // 如果有默认用户输入，则使用默认用户输入
      if (this.activityConfig.defaultUserInput) {
        this.generateForm.userCustomInput = this.activityConfig.defaultUserInput;
      }

      console.log('已应用活动配置:', this.activityConfig);
    },

    // 重置为默认配置
    resetToDefault() {
      this.$confirm('确定要重置为活动的默认配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.applyActivityConfig();
        this.$message.success('已重置为默认配置');
      }).catch(() => {
        // 用户取消
      });
    },
    // 加载广告类型配置
    loadAdTypeConfigs() {
      this.$http({
        url: this.$http.adornUrl('/activity/adtypeconfig/enabled'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.adTypeOptions = (data.list || []).map(config => ({
            value: config.typeCode,
            label: config.typeName,
            description: `适合${config.platform}的${config.contentType}文案`
          }))
          // 如果有配置且当前没有选择广告类型，默认选择第一个
          if (this.adTypeOptions.length > 0 && !this.generateForm.adType) {
            this.generateForm.adType = this.adTypeOptions[0].value
          }
        } else {
          console.error('加载广告类型配置失败:', data.msg)
          // 使用默认配置
          this.adTypeOptions = [
          ]
        }
      }).catch(error => {
        console.error('加载广告类型配置失败:', error)
        // 使用默认配置
        this.adTypeOptions = [
        ]
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activitytext/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'activityId': this.activityId,
          'appid': this.$cookie.get('appid'),
          'adType': this.dataForm.adType
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.activityId)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activitytext/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 生成文案
    generateContent() {
      // 验证输入
      if (!this.generateForm.adType) {
        this.$message.warning('请先选择广告类型');
        return;
      }

      if (!this.generateForm.promptKeyword || this.generateForm.promptKeyword.trim() === '') {
        this.$message.warning('请先输入提示词');
        return;
      }

      if (this.generateForm.nameMode === 'manual') {
        if (!this.generateForm.manualTitle || this.generateForm.manualTitle.trim() === '') {
          this.$message.warning('请先输入标题');
          return;
        }
        if (this.generateForm.manualTitle.length > 20) {
          this.$message.warning('标题不能超过20个字符');
          return;
        }
      }

      this.generating = true;
      this.generatedResult = null;
      this.formattedResult = '';

      // 调用后台接口生成文案
      this.$http({
        url: this.$http.adornUrl('/activity/activitytext/generate'),
        method: 'post',
        data: this.$http.adornData({
          activityId: this.activityId,
          model: 'deepseek-chat',
          appid: this.$cookie.get('appid'),
          nameMode: this.generateForm.nameMode,
          name: this.generateForm.nameMode === 'manual' ? this.generateForm.manualTitle.trim() : null,
          query: this.generateForm.promptKeyword.trim(), // 关键词
          adType: this.generateForm.adType,
          userCustomInput: this.generateForm.userCustomInput.trim() // 用户自定义输入
        })
      }).then(({ data }) => {
        this.generating = false;
        if (data && data.code === 200) {
          this.getDataList();
          this.$message.success('文案生成成功！');
        } else {
          this.$message.error(data.msg || '文案生成失败，请重试');
        }
      }).catch((error) => {
        this.generating = false;
        console.error('生成文案请求失败:', error);
        this.$message.error('网络请求失败，请检查网络连接');
      });
    },





    // 获取广告类型标签文本
    getAdTypeLabel(adType) {
      const option = this.adTypeOptions.find(item => item.value === adType);
      return option ? option.label : '通用文案';
    },

    // 获取广告类型标签颜色
    getAdTypeTagType(adType) {
      const typeMap = {
        douyin: 'danger',
        xiaohongshu: 'warning',
        kuaishou: 'info',
        dianping: 'success',
        meituan: 'warning',
        douyin_review: 'primary',
        weixin: 'success',
        weibo: 'primary',
        bilibili: 'danger',
        zhihu: 'primary',
        taobao: 'warning',
        jingdong: 'danger',
        general: 'info'
      };
      return typeMap[adType] || 'info';
    }
  }
}
</script>
