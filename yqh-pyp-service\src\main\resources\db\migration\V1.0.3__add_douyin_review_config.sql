-- 添加抖音点评广告类型配置（如果不存在的话）

-- 检查是否已存在抖音点评配置，如果不存在则插入
INSERT INTO `ad_type_config` (`type_code`, `type_name`, `platform`, `content_type`, `title_length`, `content_length`, `topics_count`, `topics_format`, `requirements`, `style`, `prompt_template`, `sort_order`, `status`)
SELECT 'douyin_review', '抖音点评', '抖音点评', '商户点评', '18字以内，有话题性', '90字以内，生动有趣', 6, '不带#号，用逗号分隔', '- 要有真实的体验感受\n- 语言要生动有趣，适合短视频\n- 要有视觉冲击力的描述\n- 要能引起用户互动和关注\n- 要突出商户的特色和亮点', '生动、有趣、真实体验、互动性强', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含任何#号或话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 7, 1
WHERE NOT EXISTS (
    SELECT 1 FROM `ad_type_config` WHERE `type_code` = 'douyin_review'
);

-- 更新通用文案的排序，让抖音点评排在前面
UPDATE `ad_type_config` SET `sort_order` = 8 WHERE `type_code` = 'general';
