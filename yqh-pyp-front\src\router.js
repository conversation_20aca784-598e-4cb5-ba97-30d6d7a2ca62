// import Vue from 'vue'
// import Router from 'vue-router'

// Vue.use(Router)
import { isMobilePhone } from '@/js/validate'
import { checkUserMobile } from '@/js/mobileChecker.js'
const APPID = process.env.VUE_APP_WX_APPID;
// 开发环境不使用懒加载, 因为懒加载页面太多的话会造成webpack热更新太慢, 所以只有生产环境使用懒加载
const _import = require('./import')

const router = new VueRouter({
	mode: 'hash',
	routes: [
		{
			path: '/',
			name: 'Home',
			component: () => APPID == 'wx0f8d389094ac6910' ? import('@/pages/Home') : import('@/pages/index'),
			meta: { title: '会议列表' ,isTab: true}
		}, {
			path: '/activityList',
			name: 'activityList',
			component: () => import('@/pages/Home'),
			meta: { title: '会议列表' ,isTab: true}
		}, {
			path: '/indexHome',
			name: 'indexHome',
			component: () => import('@/pages/indexHome'),
			meta: { title: '会议列表' ,isTab: true}
		}, {
			path: '/index',
			name: 'index',
			component: () => import('@/pages/index'),
			meta: { title: '首页' ,isTab: true}
		}, {
			path: '/fjsdsaishi',
			name: 'fjsdsaishi',
			component: () => import('@/pages/fjsdsaishi'),
			meta: { title: '赛事活动'}
		}, {
			path: '/fjsdsaishihistory',
			name: 'fjsdsaishihistory',
			component: () => import('@/pages/fjsdsaishihistory'),
			meta: { title: '历史赛事活动'}
		}, {
			path: '/historyHome',
			name: 'historyHome',
			component: () => import('@/pages/historyHome'),
			meta: { title: '历史会议列表' ,isTab: true}
		}, {
			path: '/wxLogin',
			name: 'wxLogin',
			component: () => import('@/pages/WxLogin'),
			meta: { title: '微信登录示例' }
		}, {
			path: '/mobileLogin',
			name: 'mobileLogin',
			component: () => import('@/pages/MobileLogin'),
			meta: { title: '登录' }
		}, {
			path: '/userAgreement',
			name: 'userAgreement',
			component: () => import('@/pages/UserAgreement'),
			meta: { title: '用户协议' }
		}, {
			path: '/privacyPolicy',
			name: 'privacyPolicy',
			component: () => import('@/pages/PrivacyPolicy'),
			meta: { title: '隐私政策' }
		}, {
			path: '/cms/index',
			name: 'cmsIndex',
			component: _import(isMobilePhone() ? 'cms/Index' : 'cms/pcIndex'),
			meta: { title: '网站' }
		}, {
			path: '/cms/content',
			name: 'cmsContent',
			component: () => import('@/pages/cms/Content'),
			meta: { title: '网站内容' }
		}, {
			path: '/cms/kuaiShare',
			name: 'cmsKuaiShare',
			component: () => import('@/pages/cms/kuaiShare'),
			meta: { title: '快手发布视频' }
		}, {
			path: '/me/mine',
			name: 'meMine',
			component: () => import('@/pages/me/mine'),
			meta: { title: '个人中心' }
		}, {
			path: '/me/applyList',
			name: 'meApplyList',
			component: () => import('@/pages/me/applyList'),
			meta: { title: '报名列表' }
		}, {
			path: '/me/applyInfo',
			name: 'meApplyInfo',
			component: () => import('@/pages/me/applyInfo'),
			meta: { title: '修改报名信息' }
		}, {
			path: '/me/applyQrCode',
			name: 'applyQrCode',
			component: () => import('@/pages/me/applyQrCode'),
			meta: { title: '参会二维码' }
		}, {
			path: '/me/hotelList',
			name: 'meHotelList',
			component: () => import('@/pages/me/hotelList'),
			meta: { title: '酒店订单' }
		}, {
			path: '/apply/index',
			name: 'applyIndex',
			component: () => import('@/pages/apply/index'),
			meta: { title: '个人报名' }
		}, {
			path: '/apply/sign',
			name: 'applySign',
			component: () => import('@/pages/apply/sign'),
			meta: { title: '电子签到' }
		}, {
			path: '/apply/success',
			name: 'applySuccess',
			component: () => import('@/pages/apply/success'),
			meta: { title: '报名成功' }
		}, {
			path: '/common/alipay',
			name: 'commonAlipay',
			component: () => import('@/pages/common/alipay'),
			meta: { title: '支付宝支付' }
		}, {
			path: '/apply/proxyList',
			name: 'applyProxyList',
			component: () => import('@/pages/apply/proxyList'),
			meta: { title: '代报名列表' }
		}, {
			path: '/apply/proxy',
			name: 'applyProxy',
			component: () => import('@/pages/apply/proxy'),
			meta: { title: '代报名' }
		}, {
			path: '/apply/proxySuccess',
			name: 'applyProxySuccess',
			component: () => import('@/pages/apply/proxySuccess'),
			meta: { title: '代报名成功' }
		}, {
			path: '/hotel/index',
			name: 'hotelIndex',
			component: () => import('@/pages/hotel/index'),
			meta: { title: '酒店列表' }
		}, {
			path: '/hotel/detail',
			name: 'hotelDetail',
			component: () => import('@/pages/hotel/detail'),
			meta: { title: '酒店详情' }
		}, {
			path: '/hotel/room',
			name: 'hotelRoom',
			component: () => import('@/pages/hotel/room'),
			meta: { title: '酒店房型选择' }
		}, {
			path: '/hotel/success',
			name: 'hotelSuccess',
			component: () => import('@/pages/hotel/success'),
			meta: { title: '酒店预订提交成功' }
		}, {
			path: '/schedules/index',
			name: 'schedulesIndex',
			component: () => import('@/pages/schedules/index'),
			meta: { title: '会议议程' }
		}, {
			path: '/schedules/simpleIndex',
			name: 'schedulesSimpleIndex',
			component: () => import('@/pages/schedules/simpleIndex'),
			meta: { title: '会议议程' }
		}, {
			path: '/schedules/indexNew',
			name: 'schedulesIndexNew',
			component: () => import('@/pages/schedules/indexNew'),
			meta: { title: '会议议程' }
		}, {
			path: '/schedules/experts',
			name: 'schedulesExperts',
			component: () => import('@/pages/schedules/experts'),
			meta: { title: '嘉宾列表' }
		}, {
			path: '/schedules/expertDetail',
			name: 'schedulesExpertDetail',
			component: () => import('@/pages/schedules/expertDetail'),
			meta: { title: '嘉宾详情' }
		}, {
			path: '/schedules/simpleExpertDetail',
			name: 'schedulesSimpleExpertDetail',
			component: () => import('@/pages/schedules/simpleExpertDetail'),
			meta: { title: '嘉宾详情' }
		}, {
			path: '/schedules/servicefee',
			name: 'servicefee',
			component: () => import('@/pages/schedules/expert/servicefee'),
			meta: { title: '劳务费签字确认' }
		}, {
			path: '/schedules/servicefeeinfo',
			name: 'servicefeeinfo',
			component: () => import('@/pages/schedules/expert/servicefeeinfo'),
			meta: { title: '劳务费信息填写' }
		}, {
			path: '/schedules/expertLink',
			name: 'expertLink',
			component: () => import('@/pages/schedules/expert/link'),
			meta: { title: '接送确认' }
		}, {
			path: '/schedules/expertTrip',
			name: 'expertTrip',
			component: () => import('@/pages/schedules/expert/trip'),
			meta: { title: '行程信息' }
		}, {
			path: '/schedules/expertTripEdit',
			name: 'expertTripEdit',
			component: () => import('@/pages/schedules/expert/tripEdit'),
			meta: { title: '行程编辑' }
		}, {
			path: '/schedules/expertTripDetail',
			name: 'expertTripDetail',
			component: () => import('@/pages/schedules/expert/tripDetail'),
			meta: { title: '行程详情' }
		}, {
			path: '/schedules/expertTripChange',
			name: 'expertTripChange',
			component: () => import('@/pages/schedules/expert/tripChange'),
			meta: { title: '申请改签' }
		},
		{
			path: '/schedules/expert/components/plane-select',
			name: 'PlaneSelect',
			component: () => import('@/pages/schedules/expert/components/plane-select.vue'),
			meta: { title: '选择航班' }
		},
		{
			path: '/schedules/expert/components/plane-change-select',
			name: 'PlaneChangeSelect',
			component: () => import('@/pages/schedules/expert/components/plane-change-select.vue'),
			meta: { title: '航班改签' }
		},
		{
			path: '/schedules/expert/components/train-select',
			name: 'TrainSelect',
			component: () => import('@/pages/schedules/expert/components/train-select.vue'),
			meta: { title: '选择火车票' }
		},  {
			path: '/schedules/expertIndex',
			name: 'expertIndex',
			component: () => import('@/pages/schedules/expert/index'),
			meta: { title: '专家首页' }
		}, {
			path: '/schedules/expertIndexCheck',
			name: 'expertIndexCheck',
			component: () => import('@/pages/schedules/expert/indexCheck'),
			meta: { title: '专家首页' }
		}, {
			path: '/schedules/expertInfo',
			name: 'expertInfo',
			component: () => import('@/pages/schedules/expert/info'),
			meta: { title: '专家基本信息' }
		}, {
			path: '/schedules/expertActivity',
			name: 'expertActivity',
			component: () => import('@/pages/schedules/expert/activity'),
			meta: { title: '我的任务' }
		}, {
			path: '/lives/index',
			name: 'livesIndex',
			component: () => import('@/pages/lives/index'),
			meta: { title: '直播列表' }
		}, {
			path: '/lives/detail',
			name: 'livesDetail',
			component: _import(isMobilePhone() ? 'lives/detail' : 'lives/pcDetail'),
			// component: () => import('@/pages/lives/detail'),
			meta: { title: '直播详情' }
		}, {
			path: '/exam/index',
			name: 'examIndex',
			component: () => import('@/pages/exam/index'),
			meta: { title: '考试&问卷' }
		}, {
			path: '/exam/detail',
			name: 'examDetail',
			component: () => import('@/pages/exam/detail'),
			meta: { title: '答题' }
		}, {
			path: '/merchant/index',
			name: 'merchantIndex',
			component: () => import('@/pages/merchant/index'),
			meta: { title: '展商列表' }
		}, {
			path: '/merchant/detail',
			name: 'merchantDetail',
			component: () => import('@/pages/merchant/detail'),
			meta: { title: '展商详情' }
		}, {
			path: '/news/index',
			name: 'newsIndex',
			component: () => import('@/pages/news/index'),
			meta: { title: '资讯列表' }
		}, {
			path: '/news/detail',
			name: 'newsDetail',
			component: () => import('@/pages/news/detail'),
			meta: { title: '资讯详情' }
		}, {
			path: '/business/index',
			name: 'businessIndex',
			component: () => import('@/pages/business/index'),
			meta: { title: '医企秀' }
		}, {
			path: '/business/detail',
			name: 'businessDetail',
			component: () => import('@/pages/business/detail'),
			meta: { title: '医企秀详情' }
		}, {
			path: '/contact',
			name: 'contact',
			component: () => import('@/pages/contact'),
			meta: { title: '联系我们' }
		}, {
			path: '/company',
			name: 'company',
			component: () => import('@/pages/company'),
			meta: { title: '公司介绍' }
		}, {
			path: '/help',
			name: 'help',
			component: () => import('@/pages/help'),
			meta: { title: '系统说明' }
		}, {
			path: '/yunhuiyi',
			name: 'yunhuiyi',
			component: () => import('@/pages/yunhuiyi'),
			meta: { title: '云会易系统说明' }
		}, {
			path: '/yqh',
			name: 'yqh',
			component: () => import('@/pages/yqh'),
			meta: { title: '易企化系统说明' }
		}, {
			path: '/yingxiao',
			name: 'yingxiao',
			component: () => import('@/pages/yingxiao'),
			meta: { title: '易企化-AI数字营销' }
		}, {
			path: '/mineNoActivity',
			name: 'mineNoActivity',
			component: () => import('@/pages/me/mineNoActivity'),
			meta: { title: '我的' ,isTab: true}
		}, {
			path: '/proxy/apply',
			name: 'proxyApply',
			component: () => import('@/pages/proxy/apply'),
			meta: { title: '商务合作' }
		}, {
			path: '/div/pintuan',
			name: 'divPintuan',
			component: () => import('@/pages/div/pintuan'),
			meta: { title: '我的拼团' }
		}, {
			path: '/div/nav',
			name: 'divNav',
			component: () => import('@/pages/div/nav'),
			meta: { title: '导航' }
		}, {
			path: '/div/zuowei',
			name: 'divZuowei',
			component: () => import('@/pages/div/zuowei'),
			meta: { title: '座位查询' }
		}, {
			path: '/bindMobile',
			name: 'bindMobile',
			component: () => import('@/pages/common/bindMobile'),
			meta: { title: '绑定手机号' }
		}, {
			path: '/salesman/scan',
			name: 'salesmanScan',
			component: () => import('@/pages/salesman/scan'),
			meta: { title: '业务员推荐' }
		}, {
			path: '/salesman/qrcode',
			name: 'salesmanQrcode',
			component: () => import('@/pages/salesman/qrcode'),
			meta: { title: '我的推广二维码' }
		}, {
			path: '/salesman/children',
			name: 'salesmanChildren',
			component: () => import('@/pages/salesman/children'),
			meta: { title: '子业务员管理' }
		}, {
			path: '/salesman/invite',
			name: 'salesmanInvite',
			component: () => import('@/pages/salesman/invite'),
			meta: { title: '邀请业务员' }
		}, {
			path: '/salesman/register',
			name: 'salesmanRegister',
			component: () => import('@/pages/salesman/register'),
			meta: { title: '业务员注册' }
		}, {
			path: '/salesman/orders',
			name: 'salesmanOrders',
			component: () => import('@/pages/salesman/orders'),
			meta: { title: '订单管理' }
		}, {
			// 新增业务员绑定相关路由
			path: '/salesman/my-salesman',
			name: 'mySalesman',
			component: () => import('@/pages/salesman/my-salesman'),
			meta: { title: '我的专属业务员' }
		}, {
			path: '/salesman/scan-qrcode',
			name: 'scanQrcode',
			component: () => import('@/pages/salesman/scan-qrcode'),
			meta: { title: '扫码绑定业务员' }
		}, {
			path: '/salesman/contact-salesman',
			name: 'contactSalesman',
			component: () => import('@/pages/salesman/contact-salesman'),
			meta: { title: '联系业务员' }
		}, {
			path: '/salesman/binding-history',
			name: 'bindingHistory',
			component: () => import('@/pages/salesman/binding-history'),
			meta: { title: '绑定历史' }
		}, {
			path: '/salesman/invite-customer',
			name: 'salesmanInviteCustomer',
			component: () => import('@/pages/salesman/invite-customer'),
			meta: { title: '邀请客户' }
		}, {
			path: '/salesman/my-customers',
			name: 'salesmanMyCustomers',
			component: () => import('@/pages/salesman/my-customers'),
			meta: { title: '我的客户' }
		}, {
			path: '/salesman/bind',
			name: 'salesmanBind',
			component: () => import('@/pages/salesman/bind'),
			meta: { title: '绑定业务员' }
		}, {
			path: '/salesman/commission-rules',
			name: 'salesmanCommissionRules',
			component: () => import('@/pages/salesman/commission-rules'),
			meta: { title: '我的抽成规则' }
		}, {
			path: '/salesman/customer-orders',
			name: 'customerOrders',
			component: () => import('@/pages/salesman/customer-orders'),
			meta: { title: '客户订单' }
		}, {
			path: '/recharge/payment',
			name: 'rechargePayment',
			component: () => import('@/pages/recharge/payment'),
			meta: { title: '充值支付' }
		}, {
			path: '/payment/success',
			name: 'paymentSuccess',
			component: () => import('@/pages/payment/success'),
			meta: { title: '支付成功' }
		}, {
			path: '/me/account',
			name: 'account',
			component: () => import('@/pages/me/account'),
			meta: { title: '我的', isTab: true }
		}, {
			path: '/me/profile',
			name: 'profile',
			component: () => import('@/pages/me/profile'),
			meta: { title: '我的', isTab: true }
		}, {
			path: '/materials/video',
			name: 'videoMaterials',
			component: () => import('@/pages/materials/video'),
			meta: { title: '素材视频' }
		}, {
			path: '/materials/finishedvideo',
			name: 'finishedVideo',
			component: () => import('@/pages/materials/finished-video'),
			meta: { title: '成品视频' }
		}, {
			path: '/materials/image',
			name: 'imageMaterials',
			component: () => import('@/pages/materials/image'),
			meta: { title: '图片素材' }
		}, {
			path: '/materials/text',
			name: 'textMaterials',
			component: () => import('@/pages/materials/text'),
			meta: { title: '文案素材' }
		}, {
			path: '/activity/edit',
			name: 'activityEdit',
			component: () => import('@/pages/activity/edit'),
			meta: { title: '修改活动' }
		}, {
			path: '/activity/create',
			name: 'activityCreate',
			component: () => import('@/pages/activity/create'),
			meta: { title: '创建活动' }
		}, {
			path: '/notifications',
			name: 'notifications',
			component: () => import('@/pages/notifications'),
			meta: { title: '消息通知' }
		}, {
			path: '/*',
			name: 'notFound',
			component: () => import('@/pages/NotFound'),
			meta: { title: '404' }
		}
	]
})

// 处理路由导航失败 - 简化版本
const originalPush = router.push;
const originalReplace = router.replace;

router.push = function push(location, onResolve, onReject) {
	if (onResolve || onReject) {
		return originalPush.call(this, location, onResolve, onReject);
	}
	return originalPush.call(this, location).catch((err) => {
		// 忽略导航重复和取消的错误
		if (err.name === 'NavigationDuplicated' || err.name === 'NavigationCancelled') {
			console.log('路由导航被忽略:', err.name);
			return Promise.resolve();
		}
		console.error('路由push错误:', err);
		return Promise.reject(err);
	});
};

router.replace = function replace(location, onResolve, onReject) {
	if (onResolve || onReject) {
		return originalReplace.call(this, location, onResolve, onReject);
	}
	return originalReplace.call(this, location).catch((err) => {
		// 忽略导航重复和取消的错误
		if (err.name === 'NavigationDuplicated' || err.name === 'NavigationCancelled') {
			console.log('路由导航被忽略:', err.name);
			return Promise.resolve();
		}
		console.error('路由replace错误:', err);
		return Promise.reject(err);
	});
};

Vue.prototype.$go = function (link) {
	if (/^(http|https):.*$/.test(link)) {//打开外部链接
        window.location.href = link;
    } else {
        router.push(link).catch(err => {
			// 忽略路由导航错误
			if (err.name !== 'NavigationDuplicated' && err.name !== 'NavigationCancelled') {
				console.error('$go 路由跳转错误:', err);
			}
		});
    }
}

// 需要检查手机号的页面路由名称列表
const ROUTES_REQUIRE_MOBILE = [
    'expertActivity',
    'expertIndexCheck',
    'activityApply',
    'activityUserApply',
    'me',
    'mine',
    'profile',
    'account'
];

// 路由守卫：检查是否需要绑定手机号
router.beforeEach((to, from, next) => {
    // 如果是绑定手机号页面或登录页面，直接通过
    if (to.name === 'bindMobile' || to.name === 'mobileLogin' || to.name === 'wxLogin') {
        next();
        return;
    }

    // 检查是否是需要手机号的页面
    if (ROUTES_REQUIRE_MOBILE.includes(to.name)) {
        // 检查用户是否已登录
        const token = Vue.cookie.get('token');
        if (!token) {
            // 未登录，跳转到登录页面
            const currentUrl = window.location.href;
            console.log("路由守卫 - 未登录，原始URL:", currentUrl);
            console.log("路由守卫 - 编码后URL:", encodeURIComponent(currentUrl));
            next({
                name: 'mobileLogin',
                query: {
                    returnUrl: encodeURIComponent(currentUrl)
                }
            });
            return;
        }

        // 已登录，检查是否有手机号
        checkUserMobile().then((hasMobile) => {
            if (!hasMobile) {
                // 没有手机号，跳转到绑定页面
                next({
                    name: 'bindMobile',
                    query: {
                        returnUrl: encodeURIComponent(window.location.href)
                    }
                });
            } else {
                // 有手机号，正常跳转
                next();
            }
        }).catch((error) => {
            console.error('检查用户手机号失败:', error);
            // 检查失败，正常跳转（避免阻塞）
            next();
        });
    } else {
        // 不需要检查手机号的页面，直接通过
        next();
    }
});

export default router;