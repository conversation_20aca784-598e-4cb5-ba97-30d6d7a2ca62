<template>
  <div class="page-container">
    <!-- 顶部背景装饰 -->
    <div class="page-header-bg"></div>

    <!-- 活动切换器 - 调试版本 -->
    <div class="activity-selector-wrapper">


      <!-- 简化版下拉菜单 -->
      <div class="activity-selector-simple" v-if="activityOptions.length > 0">
        <van-dropdown-menu>
          <van-dropdown-item
            v-model="selectedActivityId"
            :options="activityOptions"
            @change="onActivityChange"
            @open="onDropdownOpen"
            @close="onDropdownClose"
          />
        </van-dropdown-menu>
      </div>

      <!-- 备用选择器 -->
      <div class="activity-selector-fallback" v-else-if="userActivities.length > 0">
        <van-cell-group>
          <van-cell
            v-for="activity in userActivities"
            :key="activity.id"
            :title="activity.name"
            is-link
            @click="selectActivity(activity)"
            :class="{ active: selectedActivityId === activity.id }"
          />
        </van-cell-group>
      </div>

      <!-- 无活动时的选择器 -->
      <div class="activity-selector empty" v-else-if="!loading">
        <div class="empty-selector">
          <van-icon name="apps-o" class="selector-icon" />
          <span class="activity-title">暂无活动</span>
          <!-- <van-button type="primary" size="mini" @click="createActivity">创建</van-button> -->
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-selector">
        <van-loading size="20px">加载中...</van-loading>
      </div>
    </div>

    <div v-if="currentActivity" class="notification-icon-wrapper">
      <div class="notification-icon" @click="goToNotifications">
        <van-icon name="chat-o" size="20px" />
        <van-badge v-if="unreadNotificationCount > 0" :content="unreadNotificationCount" max="99" />
      </div>
    </div>

    <!-- 活动过期状态显示 -->
    <div v-if="currentActivity && currentActivityExpirationStatus" class="expiration-status-wrapper">
      <ActivityExpirationStatus
        :status="currentActivityExpirationStatus"
        :show-renewal-button="true"
        @renew="showRenewalDialog"
      />
    </div>

    <!-- 当前活动信息 -->
    <div v-if="currentActivity" class="activity-info">
      <div class="activity-banner">
        <van-image
          v-if="currentActivity.mobileBanner"
          width="100%"
          height="220px"
          :src="currentActivity.mobileBanner.split(',')[0]"
          fit="cover"
          radius="12px"
        />
        <div v-else class="default-banner">
          <div class="banner-content">
            <van-icon name="photo" size="60" class="banner-icon" />
            <p class="banner-title">{{ currentActivity.name }}</p>
            <div class="banner-decoration"></div>
          </div>
        </div>
      </div>
      <div class="activity-details">
        <div class="activity-header">
          <h2 class="activity-name">{{ currentActivity.name }}</h2>
          <div style="display: flex;gap: 10px;justify-content: flex-end;">
          <van-button
            type="primary"
            size="small"
            icon="edit"
            round
            @click="editActivityDetail"
            class="edit-button"
          >
            修改活动
          </van-button>
          <van-button
            type="info"
            size="small"
            icon="share-o"
            round
            @click="shareActivityDetail"
            class="share-button"
          >
            复制链接
          </van-button>
          <van-button
            type="info"
            size="small"
            icon="eye-o"
            round
            @click="eyeActivity"
            class="eye-button"
          >
            预览
          </van-button>
          </div>
        </div>
        <!-- <div class="activity-meta">
          <div class="meta-item">
            <div class="meta-icon-wrapper">
              <van-icon name="clock-o" />
            </div>
            <span v-if="currentActivity.startTime === currentActivity.endTime">
              {{ currentActivity.startTime ? currentActivity.startTime.substring(0, 10) : '' }}
            </span>
            <span v-else>
              {{ currentActivity.startTime ? currentActivity.startTime.substring(0, 10) : '' }} 至
              {{ currentActivity.endTime ? currentActivity.endTime.substring(5, 10) : '' }}
            </span>
          </div>
          <div class="meta-item">
            <div class="meta-icon-wrapper">
              <van-icon name="location-o" />
            </div>
            <span>{{ currentActivity.address || '地址待定' }}</span>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="operation-section">
      <div class="section-header">
        <h3 class="section-title">
          <van-icon name="setting-o" class="title-icon" />
          活动管理
        </h3>
      </div>
      <div class="operation-grid">
        <!-- <div class="operation-item primary" @click="editActivity">
          <div class="item-icon-wrapper primary">
            <van-icon name="edit" size="20" />
          </div>
          <span class="item-text">修改活动</span>
          <div class="item-arrow">
            <van-icon name="arrow" size="12" />
          </div>
        </div> -->

        <div class="operation-item" @click="manageFinishedVideos">
          <div class="item-icon-wrapper video">
            <van-icon name="video-o" size="20" />
          </div>
          <span class="item-text">成品视频</span>
          <div class="item-arrow">
            <van-icon name="arrow" size="12" />
          </div>
        </div>

        <div class="operation-item" @click="manageVideos">
          <div class="item-icon-wrapper video-alt">
            <van-icon name="video" size="20" />
          </div>
          <span class="item-text">素材视频</span>
          <div class="item-arrow">
            <van-icon name="arrow" size="12" />
          </div>
        </div>

        <div class="operation-item" @click="manageImages">
          <div class="item-icon-wrapper image">
            <van-icon name="photo-o" size="20" />
          </div>
          <span class="item-text">图片素材</span>
          <div class="item-arrow">
            <van-icon name="arrow" size="12" />
          </div>
        </div>

        <div class="operation-item" @click="manageTexts">
          <div class="item-icon-wrapper text">
            <van-icon name="notes-o" size="20" />
          </div>
          <span class="item-text">文案素材</span>
          <div class="item-arrow">
            <van-icon name="arrow" size="12" />
          </div>
        </div>

        <!-- <div class="operation-item create" @click="createActivity">
          <div class="item-icon-wrapper create">
            <van-icon name="plus" size="20" />
          </div>
          <span class="item-text">创建活动</span>
          <div class="item-arrow">
            <van-icon name="arrow" size="12" />
          </div>
        </div> -->
      </div>
    </div>

    <!-- 活动数据统计 -->
    <!-- <div v-if="currentActivity" class="activity-stats">
      <div class="stats-header">
        <h3 class="section-title">
          <van-icon name="bar-chart-o" class="title-icon" />
          活动数据
        </h3>
      </div>
      <div class="stats-grid">
        <div class="stat-card pv">
          <div class="stat-icon">
            <van-icon name="eye-o" size="24" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(currentActivity.pv || 0) }}</div>
            <div class="stat-label">浏览量</div>
          </div>
          <div class="stat-trend">
            <van-icon name="arrow-up" size="12" />
          </div>
        </div>

        <div class="stat-card uv">
          <div class="stat-icon">
            <van-icon name="friends-o" size="24" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(currentActivity.uv || 0) }}</div>
            <div class="stat-label">访客数</div>
          </div>
          <div class="stat-trend">
            <van-icon name="arrow-up" size="12" />
          </div>
        </div>

        <div class="stat-card apply">
          <div class="stat-icon">
            <van-icon name="user-o" size="24" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(currentActivity.applyCount || 0) }}</div>
            <div class="stat-label">报名数</div>
          </div>
          <div class="stat-trend">
            <van-icon name="arrow-up" size="12" />
          </div>
        </div>
      </div>
    </div> -->

    <!-- 空状态 -->
    <div v-if="!currentActivity && !loading" class="empty-state">
      <div class="empty-content">
        <van-icon name="photo-fail" size="80" class="empty-icon" />
        <h3 class="empty-title">暂无活动数据</h3>
        <p class="empty-desc">点击去购买</p>
        <van-button type="primary" class="empty-button" @click="createActivity">
          <van-icon name="plus" />
          去购买
        </van-button>
      </div>
    </div>
    <!-- <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">热门会议</div>
    </div>
    <van-card
      style="background: white"
      v-for="item in hotActivity"
      :key="item.id"
      @click="turnUrl(item)"
      :thumb="!item.mobileBanner ? 'van-icon' : item.mobileBanner.split(',')[0]"
    >
      <div slot="title" class="title">{{ item.name }}</div>
      <div
        slot="desc"
        style="padding-top: 3px; font-size: 14px; color: grey"
        v-if="item.startTime == item.endTime"
      >
        {{ item.startTime.substring(0, 10) }}
      </div>
      <div
        slot="desc"
        style="padding-top: 3px; font-size: 14px; color: grey"
        v-else
      >
        {{ item.startTime.substring(0, 10) }} 至
        {{ item.endTime.substring(5, 10) }}
      </div>
      <div slot="price" style="font-size: 14px">{{ item.address }}</div>
    </van-card>
    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">近期会议</div>
    </div>
    <van-card
      style="background: white"
      v-for="item in nearActivity"
      :key="item.id"
      @click="turnUrl(item)"
      :thumb="!item.mobileBanner ? 'van-icon' : item.mobileBanner.split(',')[0]"
    >
      <div slot="title" class="title">{{ item.name }}</div>
      <div
        slot="desc"
        style="padding-top: 3px; font-size: 14px; color: grey"
        v-if="item.startTime == item.endTime"
      >
        {{ item.startTime.substring(0, 10) }}
      </div>
      <div
        slot="desc"
        style="padding-top: 3px; font-size: 14px; color: grey"
        v-else
      >
        {{ item.startTime.substring(0, 10) }} 至
        {{ item.endTime.substring(5, 10) }}
      </div>
      <div slot="price" style="font-size: 14px">{{ item.address }}</div>
    </van-card> -->
    <sub-modal :show="showFollowModal" :qrcodeImgUrl="subUrl" @close="showFollowModal = false"></sub-modal>
    <img class="back" v-if="!userInfo.subscribe && showSub == 1 && appid == 'wx0770d56458b33c67'"
      @click="showFollowModal = true;"
      src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/0b7689735164454a876666e00f2272d9.png" alt="" />
    <img class="back" v-else-if="!userInfo.subscribe && showSub == 1" @click="showFollowModal = true;"
      src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/a040f0b471a34b178e83d94ab937476d.png" alt="" />
    <!-- <div v-if="appid == 'wx0770d56458b33c67'" @click="$router.push({
      name: 'yunhuiyi'
    })" class="bottomdiy">
      <div class="item">技术支持 易企化</div>
    </div> -->

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>

    <!-- 活动续费弹窗 -->
    <ActivityRenewalDialog
      v-model="renewalDialogVisible"
      :activity-id="selectedActivityId"
      :activity-name="currentActivity ? currentActivity.name : ''"
      :expiration-status="currentActivityExpirationStatus"
      :renewal-packages="renewalPackages"
      @renewal="handleRenewal"
    />
  </div>
</template>

<script>
import ActivityExpirationStatus from '@/components/ActivityExpirationStatus.vue';
import ActivityRenewalDialog from '@/components/ActivityRenewalDialog.vue';

export default {
  components: {
    ActivityExpirationStatus,
    ActivityRenewalDialog
  },
  data() {
    return {
      loading: false,
      userInfo: {},
      appid: "",
      renewalDialogVisible: false,
      unreadNotificationCount: 0,
    };
  },
  computed: {
    // 从 store 中获取活动相关状态
    userActivities() {
      return this.$store.state.activity.userActivities;
    },
    selectedActivityId() {
      return this.$store.state.activity.selectedActivityId;
    },
    currentActivity() {
      return this.$store.state.activity.currentActivity;
    },
    activityOptions() {
      return this.$store.state.activity.activityOptions;
    },
    currentActivityName() {
      return this.$store.getters['activity/currentActivityName'];
    },
    hasActivities() {
      return this.$store.getters['activity/hasActivities'];
    },
    // 当前活动的过期状态
    currentActivityExpirationStatus() {
      return this.$store.getters['activity/currentActivityExpirationStatus'];
    },
    // 续费套餐列表
    renewalPackages() {
      return this.$store.state.activity.renewalPackages;
    },
    // 是否有过期或即将过期的活动
    hasExpirationIssues() {
      return this.$store.getters['activity/expiredActivityCount'] > 0 ||
             this.$store.getters['activity/expiringSoonActivityCount'] > 0;
    }
  },
  watch: {
    // 监听当前活动变化
    currentActivity: {
      handler(newActivity, oldActivity) {
        if (newActivity && oldActivity && newActivity.id !== oldActivity.id) {
          console.log('Index页面检测到活动变化:', newActivity);
          // 可以在这里添加页面特定的响应逻辑
        }
      },
      deep: true
    },
    // 监听选中的活动ID变化
    selectedActivityId(newId, oldId) {
      if (newId !== oldId) {
        console.log('Index页面检测到选中活动ID变化:', newId);
        // 可以在这里添加页面特定的响应逻辑
      }
    }
  },
  mounted() {
    document.title = "我的活动";
    this.appid = this.$cookie.get("appid");
    this.initActivity();
  },
  activated() {
    // 如果页面被缓存，在激活时检查状态同步
    console.log('Index页面被激活，检查活动状态同步');
    this.checkActivitySync();
  },
  methods: {
    // 初始化活动数据
    async initActivity() {
      console.log('开始初始化活动数据...');
      this.loading = true;

      try {
        const result = await this.$store.dispatch('activity/initializeActivity', {
          api: this.$fly,
          toast: this.$toast
        });

        if (result.success) {
          console.log('活动数据初始化成功:', result.data);
          // 加载活动过期状态
          await this.loadExpirationStatus();
          // 加载未读通知数量
          await this.loadUnreadNotificationCount();
        } else {
          console.error('活动数据初始化失败:', result.error);
        }
      } catch (error) {
        console.error('初始化活动数据时发生错误:', error);
      } finally {
        this.loading = false;
      }
    },

    // 活动切换
    async onActivityChange(value) {
      console.log('Activity changed to:', value);

      try {
        const result = await this.$store.dispatch('activity/switchActivity', value);
        if (result.success) {
          console.log('活动切换成功:', result.activity);
        } else {
          console.error('活动切换失败:', result.error);
          this.$toast.fail('切换活动失败');
        }
      } catch (error) {
        console.error('切换活动时发生错误:', error);
        this.$toast.fail('切换活动失败');
      }
    },

    // 直接选择活动（备用方法）
    async selectActivity(activity) {
      console.log('Direct select activity:', activity);
      await this.onActivityChange(activity.id);
    },

    // 检查活动状态同步
    checkActivitySync() {
      // 如果全局状态中没有活动数据，重新初始化
      if (!this.hasActivities) {
        console.log('Index页面检测到没有活动数据，重新初始化');
        this.initActivity();
      }
    },

    // 下拉菜单打开事件
    onDropdownOpen() {
      console.log('Dropdown opened');
    },

    // 下拉菜单关闭事件
    onDropdownClose() {
      console.log('Dropdown closed');
    },

    // 修改活动
    editActivity() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      // 跳转到活动编辑页面
      this.$router.push({
        name: 'activityEdit',
        query: { id: this.currentActivity.id }
      });
    },

    // 修改活动详情（新的方法）
    editActivityDetail() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      // 跳转到活动编辑页面
      this.$router.push({
        path: '/activity/edit',
        query: { id: this.currentActivity.id }
      });
    },
    shareActivityDetail() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      
      let url = "https://yqihua.com/p_front/#/cms/index?id=" + this.currentActivity.id;
      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          vant.Toast('已复制到剪贴板');
        }).catch(() => {
          this.fallbackCopyText(url);
        });
      } else {
        this.fallbackCopyText(url);
      }
    },
    eyeActivity() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      
      let url = "https://yqihua.com/p_front/#/cms/index?id=" + this.currentActivity.id;
      window.open(url);
    },

    // 管理成品视频
    manageFinishedVideos() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      this.$router.push({
        path: '/materials/finished-video',
        query: { activityId: this.currentActivity.id }
      });
    },

    // 管理素材视频
    manageVideos() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      this.$router.push({
        name: 'videoMaterials',
        query: { activityId: this.currentActivity.id }
      });
    },

    // 管理成品视频
    manageFinishedVideos() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      this.$router.push({
        name: 'finishedVideo',
        query: { activityId: this.currentActivity.id }
      });
    },

    // 管理图片素材
    manageImages() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      this.$router.push({
        name: 'imageMaterials',
        query: { activityId: this.currentActivity.id }
      });
    },

    // 管理文案素材
    manageTexts() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }
      this.$router.push({
        name: 'textMaterials',
        query: { activityId: this.currentActivity.id }
      });
    },

    // 创建活动
    createActivity() {
      this.$router.push({
        name: 'salesmanScan'
      });
    },

    // 格式化数字显示
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
      }
      return num.toString();
    },

    fallbackCopyText(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        vant.Toast('已复制到剪贴板');
      } catch (err) {
        vant.Toast('复制失败');
      }
      document.body.removeChild(textArea);
    },

    // 显示续费弹窗
    showRenewalDialog() {
      if (!this.currentActivity) {
        this.$toast.fail('请先选择活动');
        return;
      }

      // 获取续费套餐列表
      this.loadRenewalPackages();
      this.renewalDialogVisible = true;
    },

    // 加载续费套餐列表
    async loadRenewalPackages() {
      try {
        await this.$store.dispatch('activity/fetchRenewalPackages');
      } catch (error) {
        console.error('获取续费套餐失败:', error);
        this.$toast.fail('获取续费套餐失败');
      }
    },

    // 处理续费
    async handleRenewal(orderData) {
      try {
        this.$toast.loading({
          message: '创建订单中...',
          forbidClick: true,
          duration: 0
        });

        const response = await this.$store.dispatch('activity/createRenewalOrder', orderData);

        this.$toast.clear();

        if (response.code === 200) {
          this.renewalDialogVisible = false;
          this.$toast.success('订单创建成功');

          // 跳转到支付页面
          this.$router.push({
            path: '/payment',
            query: {
              orderSn: response.orderSn,
              amount: response.amount,
              type: 'renewal'
            }
          });
        } else {
          this.$toast.fail(response.msg || '创建订单失败');
        }
      } catch (error) {
        this.$toast.clear();
        console.error('续费失败:', error);
        this.$toast.fail(error.message || '续费失败');
      }
    },

    // 加载活动过期状态
    async loadExpirationStatus() {
      try {
        await this.$store.dispatch('activity/fetchUserActivitiesExpirationStatus');
      } catch (error) {
        console.error('获取过期状态失败:', error);
      }
    },

    // 加载未读通知数量
    async loadUnreadNotificationCount() {
      if (!this.currentActivity) return;

      try {
        const response = await this.$fly.get('/activity/activitynotify/noReadCount', {
          params: { activityId: this.currentActivity.id }
        });

        if (response.data.code === 200) {
          this.unreadNotificationCount = response.data.result || 0;
        }
      } catch (error) {
        console.error('获取未读通知数量失败:', error);
      }
    },

    // 跳转到通知页面
    goToNotifications() {
      this.$router.push('/notifications');
    },

  }
};
</script>

<style lang="less" scoped>
// 页面整体样式
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8faff 0%, #ffffff 100%);
  position: relative;
  padding-bottom: 60px;
  // 移动端安全区域适配
  padding-top: env(safe-area-inset-top, 0);
}

.page-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 160px; // 减少背景高度
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 0;
}

.safe-area-bottom {
  height: 20px;
}

// 通知图标样式
.notification-icon-wrapper {
  position: fixed;
  top: 20px;
  right: 15px;
  z-index: 1000;
}

.notification-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.notification-icon .van-icon {
  color: #333 !important; /* 强制设置图标颜色为深灰色 */
}

.notification-icon:hover {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-icon .van-badge {
  position: absolute;
  top: -2px;
  right: -2px;
}

// 活动过期状态样式
.expiration-status-wrapper {
  margin: 0 15px 15px 15px;
  position: relative;
  z-index: 99;
}

// 活动选择器样式
.activity-selector-wrapper {
  margin: 0 15px 15px 15px; // 移除顶部边距
  position: relative;
  z-index: 100;
  // 如果需要与背景有一些间距，可以使用padding
  padding-top: 8px;
}

// 简化版选择器 - 最小样式干预
.activity-selector-simple {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  :deep(.van-dropdown-menu) {
    background: white;
  }

  :deep(.van-dropdown-menu__bar) {
    background: white;
    box-shadow: none;
  }

  :deep(.van-dropdown-menu__item) {
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 500;
  }
}

// 备用选择器样式
.activity-selector-fallback {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .van-cell {
    &.active {
      background: #f0f8ff;
      color: #1989fa;
    }
  }
}

// 空状态选择器
.activity-selector.empty {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .empty-selector {
    display: flex;
    align-items: center;
    padding: 16px 20px;

    .selector-icon {
      margin-right: 8px;
      color: #ccc;
      font-size: 18px;
    }

    .activity-title {
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: #999;
    }
  }
}

// 加载状态
.loading-selector {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
}

// 活动信息卡片
.activity-info {
  background: white;
  margin: 15px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  z-index: 10;

  .activity-banner {
    position: relative;
    overflow: hidden;

    .default-banner {
      height: 220px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      position: relative;
      overflow: hidden;

      .banner-content {
        text-align: center;
        color: white;
        z-index: 2;
        position: relative;

        .banner-icon {
          margin-bottom: 12px;
          opacity: 0.9;
        }

        .banner-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      }

      .banner-decoration {
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        z-index: 1;
      }

      &::before {
        content: '';
        position: absolute;
        top: -30%;
        left: -20%;
        width: 150px;
        height: 150px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
        z-index: 1;
      }
    }
  }

  .activity-details {
    padding: 10px 20px;

    .activity-header {
      margin-bottom: 16px;
      gap: 12px;

      .activity-name {
        margin-bottom: 10px ;
        font-size: 20px;
        font-weight: 700;
        color: #333;
        flex: 1;
        min-width: 0; // 允许文字截断
      }

      .edit-button {
        flex-shrink: 0;
        background: linear-gradient(135deg, #1989fa 0%, #1677ff 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(25, 137, 250, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        .van-icon {
          margin-right: 4px;
        }
      }
      .share-button {
        flex-shrink: 0;
        background: linear-gradient(135deg, #11943c 0%, #32c463 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(25, 137, 250, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        .van-icon {
          margin-right: 4px;
        }
      }
      .eye-button {
        flex-shrink: 0;
        background: linear-gradient(135deg, #3247a3 0%, #667eea 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(25, 137, 250, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        .van-icon {
          margin-right: 4px;
        }
      }
    }

    .activity-name {
      margin: 0 0 16px 0;
      font-size: 20px;
      font-weight: 700;
      color: #333;
      line-height: 1.4;
    }

    .activity-meta {
      .meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;
        color: #666;
        padding: 8px 0;

        .meta-icon-wrapper {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          .van-icon {
            color: white;
            font-size: 16px;
          }
        }

        span {
          flex: 1;
          font-weight: 500;
        }
      }
    }
  }
}

// 操作区域样式
.operation-section {
  margin: 15px;
  position: relative;
  z-index: 10;

  .section-header {
    margin-bottom: 16px;

    .section-title {
      display: flex;
      align-items: center;
      margin: 0;
      font-size: 18px;
      font-weight: 700;
      color: #333;

      .title-icon {
        margin-right: 8px;
        color: #667eea;
        font-size: 20px;
      }
    }
  }

  .operation-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .operation-item {
      background: white;
      border-radius: 16px;
      padding: 20px 16px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.02) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
      }

      .item-icon-wrapper {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        position: relative;
        z-index: 2;

        &.primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          .van-icon { color: white; }
        }

        &.video {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          .van-icon { color: white; }
        }

        &.video-alt {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          .van-icon { color: white; }
        }

        &.finished-video {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          .van-icon { color: white; }
        }

        &.image {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          .van-icon { color: white; }
        }

        &.text {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          .van-icon { color: white; }
        }

        &.create {
          background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          .van-icon { color: #667eea; }
        }
      }

      .item-text {
        flex: 1;
        font-size: 15px;
        font-weight: 600;
        color: #333;
        position: relative;
        z-index: 2;
      }

      .item-arrow {
        position: relative;
        z-index: 2;

        .van-icon {
          color: #ccc;
          transition: all 0.3s ease;
        }
      }

      &.primary {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        border: 1px solid rgba(102, 126, 234, 0.1);
      }

      &.create {
        background: linear-gradient(135deg, rgba(168, 237, 234, 0.1) 0%, rgba(254, 214, 227, 0.1) 100%);
        border: 1px solid rgba(102, 126, 234, 0.1);
      }
    }
  }
}

// 统计数据样式
.activity-stats {
  margin: 15px;
  position: relative;
  z-index: 10;

  .stats-header {
    margin-bottom: 16px;

    .section-title {
      display: flex;
      align-items: center;
      margin: 0;
      font-size: 18px;
      font-weight: 700;
      color: #333;

      .title-icon {
        margin-right: 8px;
        color: #667eea;
        font-size: 20px;
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;

    .stat-card {
      background: white;
      border-radius: 16px;
      padding: 20px 16px;
      text-align: center;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &.pv::before {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
      }

      &.uv::before {
        background: linear-gradient(135deg, rgba(240, 147, 251, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
      }

      &.apply::before {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.05) 0%, rgba(56, 249, 215, 0.05) 100%);
      }

      &:hover::before {
        opacity: 1;
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
        position: relative;
        z-index: 2;
      }

      &.pv .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        .van-icon { color: white; }
      }

      &.uv .stat-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        .van-icon { color: white; }
      }

      &.apply .stat-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        .van-icon { color: white; }
      }

      .stat-content {
        position: relative;
        z-index: 2;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 4px;
          background: linear-gradient(135deg, #333 0%, #666 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .stat-label {
          font-size: 12px;
          color: #999;
          font-weight: 500;
        }
      }

      .stat-trend {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: rgba(67, 233, 123, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;

        .van-icon {
          color: #43e97b;
          font-size: 10px;
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  margin: 15px;
  padding: 60px 20px;
  text-align: center;
  position: relative;
  z-index: 10;

  .empty-content {
    background: white;
    border-radius: 20px;
    padding: 40px 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

    .empty-icon {
      color: #ddd;
      margin-bottom: 20px;
    }

    .empty-title {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .empty-desc {
      margin: 0 0 24px 0;
      font-size: 14px;
      color: #999;
      line-height: 1.5;
    }

    .empty-button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      padding: 12px 24px;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

      .van-icon {
        margin-right: 4px;
      }
    }
  }
}

// 通用样式
.title {
  font-size: 18px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 响应式优化
@media (max-width: 375px) {
  .operation-grid {
    .operation-item {
      padding: 16px 12px;

      .item-icon-wrapper {
        width: 40px;
        height: 40px;
      }

      .item-text {
        font-size: 14px;
      }
    }
  }

  .stats-grid {
    .stat-card {
      padding: 16px 12px;

      .stat-icon {
        width: 36px;
        height: 36px;
      }

      .stat-value {
        font-size: 20px;
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.activity-info,
.operation-section,
.activity-stats {
  animation: fadeInUp 0.6s ease-out;
}

.operation-section {
  animation-delay: 0.1s;
}

.activity-stats {
  animation-delay: 0.2s;
}

// 滚动条美化
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
</style>