package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeUsageService;
import com.cjy.pyp.modules.salesman.dao.SalesmanCommissionRecordDao;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.enums.CalculationTypeEnum;
import com.cjy.pyp.modules.salesman.enums.CommissionTypeEnum;
import com.cjy.pyp.modules.salesman.enums.SettlementStatusEnum;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionConfigService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.github.pagehelper.PageHelper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 业务员佣金记录服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Service("salesmanCommissionRecordService")
public class SalesmanCommissionRecordServiceImpl extends ServiceImpl<SalesmanCommissionRecordDao, SalesmanCommissionRecordEntity> 
        implements SalesmanCommissionRecordService {

    @Autowired
    private SalesmanCommissionConfigService commissionConfigService;
    
    @Autowired
    private ActivityRechargeRecordService rechargeRecordService;
    
    @Autowired
    private ActivityRechargeUsageService rechargeUsageService;

    @Override
    public List<SalesmanCommissionRecordEntity> queryPage(Map<String, Object> params) {
        int page = Integer.parseInt((String) params.get("page"));
        int limit = Integer.parseInt((String) params.get("limit"));
        PageHelper.startPage(page,limit);
        List<SalesmanCommissionRecordEntity> list = baseMapper.queryPage(params);
        return list;

    }

    @Override
    public Map<String, Object> getCommissionStats(Map<String, Object> params) {
        return baseMapper.getCommissionStats(params);
    }

    @Override
    public List<SalesmanCommissionRecordEntity> getUnsettledRecords(Map<String, Object> params) {
        return baseMapper.getUnsettledRecords(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateSettlementStatus(List<Long> recordIds, Integer settlementStatus, 
                                          String settlementBatchNo, String settlementRemarks) {
        return baseMapper.batchUpdateSettlementStatus(recordIds, settlementStatus, 
                                                     settlementBatchNo, new Date().toString(), settlementRemarks);
    }

    @Override
    public boolean existsByBusiness(String businessType, Long businessId, String appid) {
        return baseMapper.existsByBusiness(businessType, businessId, appid);
    }

    @Override
    public List<Map<String, Object>> getCommissionSummary(Map<String, Object> params) {
        return baseMapper.getCommissionSummary(params);
    }

    @Override
    public List<Map<String, Object>> getCommissionTrend(Map<String, Object> params) {
        return baseMapper.getCommissionTrend(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionRecordEntity generateCreateActivityCommission(Long salesmanId, Long rechargeRecordId, 
                                                                          BigDecimal orderAmount, String appid) {
        // 检查是否已生成佣金
        if (existsByBusiness(CommissionTypeEnum.CREATE_ACTIVITY.getBusinessType(), rechargeRecordId, appid)) {
            return null;
        }
        
        // 获取佣金配置
        SalesmanCommissionConfigEntity config = commissionConfigService.getByTypeAndSalesman(
                salesmanId, CommissionTypeEnum.CREATE_ACTIVITY.getCode(), appid);
        if (config == null || config.getStatus() != 1) {
            return null;
        }
        
        // 获取充值记录信息
        ActivityRechargeRecordEntity rechargeRecord = rechargeRecordService.getById(rechargeRecordId);
        if (rechargeRecord == null) {
            return null;
        }
        
        // 计算佣金金额
        BigDecimal commissionAmount = calculateCommissionAmount(
                config.getCommissionType(), config.getCalculationType(), 
                config.getCommissionValue(), orderAmount, 
                config.getMinAmount(), config.getMaxAmount());
        
        // 创建佣金记录
        SalesmanCommissionRecordEntity record = new SalesmanCommissionRecordEntity();
        record.setSalesmanId(salesmanId);
        record.setUserId(rechargeRecord.getUserId());
        record.setActivityId(rechargeRecord.getActivityId());
        record.setCommissionType(CommissionTypeEnum.CREATE_ACTIVITY.getCode());
        record.setBusinessType(CommissionTypeEnum.CREATE_ACTIVITY.getBusinessType());
        record.setBusinessId(rechargeRecordId);
        record.setOrderAmount(orderAmount);
        record.setCommissionRate(config.getCalculationType() == CalculationTypeEnum.PERCENTAGE.getCode() ? 
                                config.getCommissionValue() : null);
        record.setCommissionAmount(commissionAmount);
        record.setCalculationType(config.getCalculationType());
        record.setSettlementStatus(SettlementStatusEnum.UNSETTLED.getCode());
        record.setBusinessTime(rechargeRecord.getCreateOn());
        record.setDescription("创建活动佣金 - 订单号：" + rechargeRecord.getOrderSn());
        record.setAppid(appid);
        
        this.save(record);
        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionRecordEntity generateRechargeCountCommission(Long salesmanId, Long rechargeRecordId, 
                                                                         BigDecimal orderAmount, String appid) {
        // 检查是否已生成佣金
        if (existsByBusiness(CommissionTypeEnum.RECHARGE_COUNT.getBusinessType(), rechargeRecordId, appid)) {
            return null;
        }
        
        // 获取佣金配置
        SalesmanCommissionConfigEntity config = commissionConfigService.getByTypeAndSalesman(
                salesmanId, CommissionTypeEnum.RECHARGE_COUNT.getCode(), appid);
        if (config == null || config.getStatus() != 1) {
            return null;
        }
        
        // 获取充值记录信息
        ActivityRechargeRecordEntity rechargeRecord = rechargeRecordService.getById(rechargeRecordId);
        if (rechargeRecord == null) {
            return null;
        }
        
        // 计算佣金金额
        BigDecimal commissionAmount = calculateCommissionAmount(
                config.getCommissionType(), config.getCalculationType(), 
                config.getCommissionValue(), orderAmount, 
                config.getMinAmount(), config.getMaxAmount());
        
        // 创建佣金记录
        SalesmanCommissionRecordEntity record = new SalesmanCommissionRecordEntity();
        record.setSalesmanId(salesmanId);
        record.setUserId(rechargeRecord.getUserId());
        record.setActivityId(rechargeRecord.getActivityId());
        record.setCommissionType(CommissionTypeEnum.RECHARGE_COUNT.getCode());
        record.setBusinessType(CommissionTypeEnum.RECHARGE_COUNT.getBusinessType());
        record.setBusinessId(rechargeRecordId);
        record.setOrderAmount(orderAmount);
        record.setCommissionRate(config.getCalculationType() == CalculationTypeEnum.PERCENTAGE.getCode() ? 
                                config.getCommissionValue() : null);
        record.setCommissionAmount(commissionAmount);
        record.setCalculationType(config.getCalculationType());
        record.setSettlementStatus(SettlementStatusEnum.UNSETTLED.getCode());
        record.setBusinessTime(rechargeRecord.getCreateOn());
        record.setDescription("充值次数佣金 - 订单号：" + rechargeRecord.getOrderSn());
        record.setAppid(appid);
        
        this.save(record);
        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionRecordEntity generateUserForwardCommission(Long salesmanId, Long usageRecordId, String appid) {
        // 检查是否已生成佣金
        if (existsByBusiness(CommissionTypeEnum.USER_FORWARD.getBusinessType(), usageRecordId, appid)) {
            return null;
        }
        
        // 获取佣金配置
        SalesmanCommissionConfigEntity config = commissionConfigService.getByTypeAndSalesman(
                salesmanId, CommissionTypeEnum.USER_FORWARD.getCode(), appid);
        if (config == null || config.getStatus() != 1) {
            return null;
        }
        
        // 获取使用记录信息
        ActivityRechargeUsageEntity usageRecord = rechargeUsageService.getById(usageRecordId);
        if (usageRecord == null || usageRecord.getUsageType() != 3) { // 3-转发
            return null;
        }
        
        // 计算佣金金额（转发佣金只支持固定金额）
        BigDecimal commissionAmount = config.getCommissionValue();
        
        // 创建佣金记录
        SalesmanCommissionRecordEntity record = new SalesmanCommissionRecordEntity();
        record.setSalesmanId(salesmanId);
        record.setUserId(usageRecord.getUserId());
        record.setActivityId(usageRecord.getActivityId());
        record.setCommissionType(CommissionTypeEnum.USER_FORWARD.getCode());
        record.setBusinessType(CommissionTypeEnum.USER_FORWARD.getBusinessType());
        record.setBusinessId(usageRecordId);
        record.setOrderAmount(null); // 转发没有订单金额
        record.setCommissionRate(null);
        record.setCommissionAmount(commissionAmount);
        record.setCalculationType(CalculationTypeEnum.FIXED_AMOUNT.getCode());
        record.setSettlementStatus(SettlementStatusEnum.UNSETTLED.getCode());
        record.setBusinessTime(usageRecord.getCreateOn());
        record.setDescription("用户转发佣金 - 转发次数：" + usageRecord.getUsageCount());
        record.setAppid(appid);
        
        this.save(record);
        return record;
    }

    @Override
    public BigDecimal calculateCommissionAmount(Integer commissionType, Integer calculationType, 
                                               BigDecimal commissionValue, BigDecimal orderAmount, 
                                               BigDecimal minAmount, BigDecimal maxAmount) {
        BigDecimal commissionAmount;
        
        if (calculationType == CalculationTypeEnum.FIXED_AMOUNT.getCode()) {
            // 固定金额
            commissionAmount = commissionValue;
        } else if (calculationType == CalculationTypeEnum.PERCENTAGE.getCode()) {
            // 百分比计算
            if (orderAmount == null || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return BigDecimal.ZERO;
            }
            commissionAmount = orderAmount.multiply(commissionValue).setScale(2, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.ZERO;
        }
        
        // 应用最小和最大金额限制
        if (minAmount != null && commissionAmount.compareTo(minAmount) < 0) {
            commissionAmount = minAmount;
        }
        if (maxAmount != null && commissionAmount.compareTo(maxAmount) > 0) {
            commissionAmount = maxAmount;
        }
        
        return commissionAmount;
    }
}
