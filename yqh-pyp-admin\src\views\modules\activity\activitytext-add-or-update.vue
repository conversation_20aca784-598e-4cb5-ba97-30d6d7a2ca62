<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="使用模型" prop="model">
        <el-input v-model="dataForm.model" placeholder="使用模型"></el-input>
      </el-form-item>
      <el-form-item label="提示词" prop="prompt">
        <el-input v-model="dataForm.prompt" placeholder="提示词"></el-input>
      </el-form-item>
      <!-- <el-form-item label="结果摘要" prop="resultSummary">
        <el-input v-model="dataForm.resultSummary" placeholder="结果摘要"></el-input>
      </el-form-item>
      <el-form-item label="搜索结果(JSON格式)" prop="searchResults">
        <el-input v-model="dataForm.searchResults" placeholder="搜索结果(JSON格式)"></el-input>
      </el-form-item>
      <el-form-item label="AI思考过程" prop="thinkProcess">
        <el-input v-model="dataForm.thinkProcess" placeholder="AI思考过程"></el-input>
      </el-form-item>
      <el-form-item label="自定义输入" prop="query">
        <el-input v-model="dataForm.query" placeholder="自定义输入"></el-input>
      </el-form-item> -->
      <el-form-item label="标题" prop="name">
        <el-input v-model="dataForm.name" placeholder="标题"></el-input>
      </el-form-item>
      <el-form-item label="提示词" prop="title">
        <el-input v-model="dataForm.title" placeholder="提示词"></el-input>
      </el-form-item>
      <!-- <el-form-item label="生成文案">
        <el-button type="primary" @click="generateContent" :loading="generating">
          {{ generating ? '生成中...' : '生成文案' }}
        </el-button>
        <span style="margin-left: 10px; color: #999; font-size: 12px;">
          点击按钮根据提示词自动生成文案内容
        </span>
      </el-form-item> -->
      <el-form-item label="文案" prop="result">
        <el-input
          v-model="dataForm.result"
          type="textarea"
          :rows="8"
          placeholder="文案内容将在这里显示，也可以手动编辑">
        </el-input>
      </el-form-item>
      <el-form-item label="使用次数" prop="useCount">
        <el-input v-model="dataForm.useCount" placeholder="使用次数"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      generating: false, // 生成文案状态
      dataForm: {
        repeatToken: '',
        id: 0,

        activityId: '',

        paixu: '',

        useCount: '',

        model: 'deepseek-chat',

        prompt: '',

        resultSummary: '',

        searchResults: '',

        thinkProcess: '',

        query: '',

        name: '',

        title: '',

        result: ''
      },
      dataRule: {
        activityId: [
          { required: true, message: '会议id不能为空', trigger: 'blur' }
        ],
        paixu: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        useCount: [
          { required: true, message: '使用次数不能为空', trigger: 'blur' }
        ],
        model: [
          { required: true, message: '使用模型不能为空', trigger: 'blur' }
        ],
        prompt: [
          { required: true, message: '提示词不能为空', trigger: 'blur' }
        ],
        resultSummary: [
          { required: true, message: '结果摘要不能为空', trigger: 'blur' }
        ],
        searchResults: [
          { required: true, message: '搜索结果(JSON格式)不能为空', trigger: 'blur' }
        ],
        thinkProcess: [
          { required: true, message: 'AI思考过程不能为空', trigger: 'blur' }
        ],
        query: [
          { required: true, message: '自定义输入不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '提示词不能为空', trigger: 'blur' }
        ],
        result: [
          { required: true, message: '文案不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id, activityId) {
      this.getToken();
      this.dataForm.id = id || 0
      this.dataForm.activityId = activityId
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()

        // 设置默认提示词
        if (!this.dataForm.id) {
          this.dataForm.title = '根据"易企化,碰一碰AI爆店码,获客营销"这个提示词，帮我生成以下内容，并以JSON格式返回（键名使用英文）：- 标题（title，20字以内）- 内容（content，100字以内）- 话题（topics，10个，用逗号分隔，不带#）';
        }

        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activitytext/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.activityText.activityId
              this.dataForm.paixu = data.activityText.paixu
              this.dataForm.useCount = data.activityText.useCount
              this.dataForm.model = data.activityText.model
              this.dataForm.prompt = data.activityText.prompt
              this.dataForm.resultSummary = data.activityText.resultSummary
              this.dataForm.searchResults = data.activityText.searchResults
              this.dataForm.thinkProcess = data.activityText.thinkProcess
              this.dataForm.query = data.activityText.query
              this.dataForm.name = data.activityText.name
              this.dataForm.title = data.activityText.title
              this.dataForm.result = data.activityText.result
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 生成文案
    generateContent() {
      // 检查提示词是否为空
      if (!this.dataForm.title || this.dataForm.title.trim() === '') {
        this.$message.warning('请先输入提示词');
        return;
      }

      // 设置默认提示词（如果用户没有输入）
      const defaultPrompt = '根据"易企化,碰一碰AI爆店码,获客营销"这个提示词，帮我生成以下内容，并以JSON格式返回（键名使用英文）：- 标题（title，20字以内）- 内容（content，100字以内）- 话题（topics，10个，用逗号分隔，不带#）';
      const finalPrompt = this.dataForm.title.trim() || defaultPrompt;

      this.generating = true;

      // 调用后台接口生成文案
      this.$http({
        url: this.$http.adornUrl('/activity/activitytext/generate'),
        method: 'post',
        data: this.$http.adornData({
          prompt: finalPrompt,
          appid: this.$cookie.get('appid')
        })
      }).then(({ data }) => {
        this.generating = false;
        if (data && data.code === 200) {
          try {
            // 解析返回的JSON数据
            const result = typeof data.result === 'string' ? JSON.parse(data.result) : data.result;

            // 格式化生成的内容
            const formattedContent = this.formatGeneratedContent(result);

            // 将生成的内容填入文案字段
            this.dataForm.result = formattedContent;

            // 如果标题为空，使用生成的标题
            if (!this.dataForm.name && result.title) {
              this.dataForm.name = result.title;
            }

            this.$message.success('文案生成成功！');
          } catch (error) {
            console.error('解析生成结果失败:', error);
            this.$message.error('生成结果解析失败，请重试');
          }
        } else {
          this.$message.error(data.msg || '文案生成失败，请重试');
        }
      }).catch((error) => {
        this.generating = false;
        console.error('生成文案请求失败:', error);
        this.$message.error('网络请求失败，请检查网络连接');
      });
    },
    // 格式化生成的内容
    formatGeneratedContent(result) {
      let formatted = '';

      if (result.title) {
        formatted += `标题：${result.title}\n\n`;
      }

      if (result.content) {
        formatted += `内容：${result.content}\n\n`;
      }

      if (result.topics) {
        const topics = Array.isArray(result.topics) ? result.topics.join(', ') : result.topics;
        formatted += `话题：${topics}`;
      }

      return formatted;
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activitytext/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'activityId': this.dataForm.activityId,
              'paixu': this.dataForm.paixu,
              'useCount': this.dataForm.useCount,
              'model': this.dataForm.model,
              'prompt': this.dataForm.prompt,
              'resultSummary': this.dataForm.resultSummary,
              'searchResults': this.dataForm.searchResults,
              'thinkProcess': this.dataForm.thinkProcess,
              'query': this.dataForm.query,
              'name': this.dataForm.name,
              'title': this.dataForm.title,
              'result': this.dataForm.result,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
